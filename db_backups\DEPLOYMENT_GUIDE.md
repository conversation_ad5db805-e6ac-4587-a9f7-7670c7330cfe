# Interview Notification System - Production Database Migration Guide

## Overview
This guide provides step-by-step instructions for deploying the Interview Notification System database changes to the production environment.

## Prerequisites
- SSH access to production server
- MySQL client installed locally
- Production database credentials (provided in DB_online_credentials.md)
- Backup of current database

## Migration Files Created
1. `2025-01-29-120000_AddInterviewNotificationFields.php` - CodeIgniter 4 migration file
2. `production_migration.sql` - Direct SQL migration script
3. `backup_before_interview_migration.sql` - Backup verification script
4. `deploy_interview_migration.php` - Automated deployment script

## Step-by-Step Deployment Process

### Step 1: Establish SSH Tunnel
Open a new command prompt and run:
```bash
ssh -L 3307:127.0.0.1:3306 <EMAIL> -N
```
**Password:** `dakoiianzii`

**Important:** Keep this terminal window open throughout the migration process.

### Step 2: Create Database Backup
Open a second command prompt and create a backup:
```bash
mysqldump -h 127.0.0.1 -P 3307 -u dakoiim1_ders_db_admin -p dakoiim1_ders_db appx_application_details > backup_before_interview_migration_$(date +%Y%m%d_%H%M%S).sql
```
**Password:** `dakoiianzii`

### Step 3: Verify Current Database State
Connect to the production database:
```bash
mysql -h 127.0.0.1 -P 3307 -u dakoiim1_ders_db_admin -p
```
**Password:** `dakoiianzii`

Run verification queries:
```sql
USE dakoiim1_ders_db;

-- Check current table structure
DESCRIBE appx_application_details;

-- Check for existing interview notification fields
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'dakoiim1_ders_db' 
  AND TABLE_NAME = 'appx_application_details' 
  AND COLUMN_NAME LIKE '%interview_notified%';

-- Count total records
SELECT COUNT(*) as total_records FROM appx_application_details;
```

### Step 4: Execute Migration
**Option A: Using SQL Script (Recommended)**
```sql
SOURCE /path/to/ders/db_backups/production_migration.sql;
```

**Option B: Manual SQL Commands**
```sql
USE dakoiim1_ders_db;

-- Add interview notification fields
ALTER TABLE appx_application_details 
ADD COLUMN interview_notified_at DATETIME NULL 
COMMENT 'Timestamp when interview notification was last sent';

ALTER TABLE appx_application_details 
ADD COLUMN interview_notified_by INT(11) NULL 
COMMENT 'User ID who sent the interview notification';

ALTER TABLE appx_application_details 
ADD COLUMN interview_notified_count INT(11) NULL DEFAULT 0 
COMMENT 'Number of times interview notification email was sent';

ALTER TABLE appx_application_details 
ADD COLUMN interview_notified_email VARCHAR(255) NULL 
COMMENT 'Email address where interview notification was sent';

ALTER TABLE appx_application_details 
ADD COLUMN interview_notified_remarks VARCHAR(50) NULL 
COMMENT 'Type of notification sent: scheduled or without_schedule';

-- Add performance indexes
CREATE INDEX idx_interview_notified_at 
ON appx_application_details (interview_notified_at);

CREATE INDEX idx_interview_notified_by 
ON appx_application_details (interview_notified_by);

CREATE INDEX idx_interview_notification_tracking 
ON appx_application_details (interview_notified_count, interview_notified_at);
```

### Step 5: Verify Migration Success
```sql
-- Check new table structure
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'dakoiim1_ders_db' 
  AND TABLE_NAME = 'appx_application_details' 
  AND COLUMN_NAME LIKE '%interview_notified%'
ORDER BY ORDINAL_POSITION;

-- Verify indexes
SHOW INDEX FROM appx_application_details WHERE Key_name LIKE '%interview%';

-- Test new fields
SELECT id, application_number, 
       interview_notified_at, 
       interview_notified_by, 
       interview_notified_count, 
       interview_notified_email, 
       interview_notified_remarks
FROM appx_application_details 
LIMIT 3;

-- Verify record count unchanged
SELECT COUNT(*) as total_records FROM appx_application_details;
```

### Step 6: Update Production Code
1. Deploy the interview-feature branch code to production server
2. Ensure all new files are uploaded:
   - Updated `InterviewManagementController.php`
   - Updated `interviews_schedule.php` view
   - Updated `interview_notification.php` email template
   - Updated `AppxApplicationDetailsModel.php`
   - Updated `Routes.php`

### Step 7: Test Production System
1. Navigate to the interview schedule page
2. Test the modal notification system
3. Send test notifications (both types)
4. Verify email delivery
5. Check database updates

## Expected Results After Migration

### Database Schema Changes
- ✅ `interview_notified_at` DATETIME NULL
- ✅ `interview_notified_by` INT(11) NULL  
- ✅ `interview_notified_count` INT(11) NULL DEFAULT 0
- ✅ `interview_notified_email` VARCHAR(255) NULL
- ✅ `interview_notified_remarks` VARCHAR(50) NULL
- ✅ 3 performance indexes added

### Application Features
- ✅ Dual notification types (scheduled/without schedule)
- ✅ Modal interface for notification selection
- ✅ Real-time notification count display
- ✅ Page position preservation after sending
- ✅ Enhanced visual feedback and animations
- ✅ Comprehensive notification tracking

## Rollback Procedure (If Needed)

### Emergency Rollback
```sql
-- Remove indexes
DROP INDEX IF EXISTS idx_interview_notified_at ON appx_application_details;
DROP INDEX IF EXISTS idx_interview_notified_by ON appx_application_details;
DROP INDEX IF EXISTS idx_interview_notification_tracking ON appx_application_details;

-- Remove fields
ALTER TABLE appx_application_details DROP COLUMN IF EXISTS interview_notified_remarks;
ALTER TABLE appx_application_details DROP COLUMN IF EXISTS interview_notified_email;
ALTER TABLE appx_application_details DROP COLUMN IF EXISTS interview_notified_count;
ALTER TABLE appx_application_details DROP COLUMN IF EXISTS interview_notified_by;
ALTER TABLE appx_application_details DROP COLUMN IF EXISTS interview_notified_at;
```

### Restore from Backup
```bash
mysql -h 127.0.0.1 -P 3307 -u dakoiim1_ders_db_admin -p dakoiim1_ders_db < backup_before_interview_migration.sql
```

## Post-Migration Checklist
- [ ] SSH tunnel established successfully
- [ ] Database backup created and verified
- [ ] Migration executed without errors
- [ ] All 5 new fields added correctly
- [ ] All 3 indexes created successfully
- [ ] Record count unchanged
- [ ] New fields are queryable
- [ ] Production code deployed
- [ ] Interview notification system tested
- [ ] Email notifications working
- [ ] Modal interface functional
- [ ] Position preservation working
- [ ] No errors in application logs

## Support Information
- **Migration Date:** 2025-01-29
- **Feature Branch:** interview-feature
- **Database:** dakoiim1_ders_db
- **Backup Location:** db_backups/backup_before_interview_migration_*.sql
- **Migration Files:** app/Database/Migrations/2025-01-29-120000_AddInterviewNotificationFields.php

## Contact
If issues arise during migration, refer to the rollback procedures above or restore from the backup created in Step 2.
