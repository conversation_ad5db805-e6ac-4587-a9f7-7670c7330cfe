<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

/**
 * Migration: Add Interview Notification Fields
 * 
 * This migration adds comprehensive interview notification tracking fields
 * to the appx_application_details table to support the Interview Notification System.
 * 
 * Features supported:
 * - Dual email notification types (scheduled/without schedule)
 * - Notification count tracking
 * - Email delivery tracking
 * - Notification type tracking
 * - User audit trail
 * 
 * Created: 2025-01-29
 * Author: Interview Notification System Feature
 * Related Feature: interview-feature branch
 */
class AddInterviewNotificationFields extends Migration
{
    /**
     * Apply the migration
     * 
     * Adds the following fields to appx_application_details table:
     * - interview_notified_at: Timestamp of last notification sent
     * - interview_notified_by: User ID who sent the notification
     * - interview_notified_count: Number of notifications sent
     * - interview_notified_email: Email address where notification was sent
     * - interview_notified_remarks: Type of notification (scheduled/without_schedule)
     */
    public function up()
    {
        // Check if table exists
        if (!$this->db->tableExists('appx_application_details')) {
            throw new \Exception('Table appx_application_details does not exist. Cannot add interview notification fields.');
        }

        // Get the forge instance
        $forge = \Config\Database::forge();

        // Define the fields to add
        $fields = [
            'interview_notified_at' => [
                'type' => 'DATETIME',
                'null' => true,
                'comment' => 'Timestamp when interview notification was last sent'
            ],
            'interview_notified_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true,
                'comment' => 'User ID who sent the interview notification'
            ],
            'interview_notified_count' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true,
                'default' => 0,
                'comment' => 'Number of times interview notification email was sent'
            ],
            'interview_notified_email' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
                'comment' => 'Email address where interview notification was sent'
            ],
            'interview_notified_remarks' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
                'comment' => 'Type of notification sent: scheduled or without_schedule'
            ]
        ];

        // Check which fields already exist to avoid conflicts
        $existingFields = $this->db->getFieldNames('appx_application_details');
        $fieldsToAdd = [];

        foreach ($fields as $fieldName => $fieldConfig) {
            if (!in_array($fieldName, $existingFields)) {
                $fieldsToAdd[$fieldName] = $fieldConfig;
                echo "Will add field: {$fieldName}\n";
            } else {
                echo "Field {$fieldName} already exists, skipping...\n";
            }
        }

        // Add only the fields that don't exist
        if (!empty($fieldsToAdd)) {
            $forge->addColumn('appx_application_details', $fieldsToAdd);
            echo "Successfully added " . count($fieldsToAdd) . " interview notification fields.\n";
        } else {
            echo "All interview notification fields already exist.\n";
        }

        // Add indexes for better performance
        $this->addIndexes();
    }

    /**
     * Rollback the migration
     * 
     * Removes all interview notification fields from appx_application_details table
     */
    public function down()
    {
        // Check if table exists
        if (!$this->db->tableExists('appx_application_details')) {
            echo "Table appx_application_details does not exist. Nothing to rollback.\n";
            return;
        }

        $forge = \Config\Database::forge();
        
        // List of fields to remove
        $fieldsToRemove = [
            'interview_notified_at',
            'interview_notified_by', 
            'interview_notified_count',
            'interview_notified_email',
            'interview_notified_remarks'
        ];

        // Check which fields exist before trying to remove them
        $existingFields = $this->db->getFieldNames('appx_application_details');
        $fieldsToActuallyRemove = [];

        foreach ($fieldsToRemove as $fieldName) {
            if (in_array($fieldName, $existingFields)) {
                $fieldsToActuallyRemove[] = $fieldName;
                echo "Will remove field: {$fieldName}\n";
            } else {
                echo "Field {$fieldName} does not exist, skipping...\n";
            }
        }

        // Remove the fields that exist
        if (!empty($fieldsToActuallyRemove)) {
            $forge->dropColumn('appx_application_details', $fieldsToActuallyRemove);
            echo "Successfully removed " . count($fieldsToActuallyRemove) . " interview notification fields.\n";
        } else {
            echo "No interview notification fields to remove.\n";
        }
    }

    /**
     * Add database indexes for better performance
     */
    private function addIndexes()
    {
        try {
            // Add index on interview_notified_at for date-based queries
            $this->db->query("
                CREATE INDEX IF NOT EXISTS idx_interview_notified_at 
                ON appx_application_details (interview_notified_at)
            ");

            // Add index on interview_notified_by for user-based queries  
            $this->db->query("
                CREATE INDEX IF NOT EXISTS idx_interview_notified_by 
                ON appx_application_details (interview_notified_by)
            ");

            // Add composite index for notification tracking queries
            $this->db->query("
                CREATE INDEX IF NOT EXISTS idx_interview_notification_tracking 
                ON appx_application_details (interview_notified_count, interview_notified_at)
            ");

            echo "Successfully added performance indexes for interview notification fields.\n";
        } catch (\Exception $e) {
            echo "Warning: Could not add indexes - " . $e->getMessage() . "\n";
            // Don't fail the migration if indexes can't be added
        }
    }
}
