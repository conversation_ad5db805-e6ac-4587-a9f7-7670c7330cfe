-- =====================================================
-- Interview Notification System - Database Updates
-- ALTER TABLE Statements for Production Deployment
-- =====================================================
-- Branch: interview-feature
-- Target Database: dakoiim1_ders_db
-- Target Table: appx_application_details
-- Date: 2025-01-29
-- 
-- This script contains all ALTER TABLE statements needed
-- to add Interview Notification System fields to production
-- =====================================================

-- Use the production database
USE dakoiim1_ders_db;

-- =====================================================
-- PRE-MIGRATION VERIFICATION
-- =====================================================

-- Check current table structure
SELECT 'Current table structure before migration:' as info;
DESCRIBE appx_application_details;

-- Check if any interview notification fields already exist
SELECT 'Checking for existing interview notification fields:' as info;
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'dakoiim1_ders_db' 
  AND TABLE_NAME = 'appx_application_details' 
  AND COLUMN_NAME LIKE '%interview_notified%';

-- Count records before migration
SELECT 'Total records before migration:' as info;
SELECT COUNT(*) as record_count FROM appx_application_details;

-- =====================================================
-- ALTER TABLE STATEMENTS - INTERVIEW NOTIFICATION FIELDS
-- =====================================================

-- 1. Add interview_notified_at field
-- Stores timestamp when interview notification was last sent
ALTER TABLE appx_application_details 
ADD COLUMN interview_notified_at DATETIME NULL 
COMMENT 'Timestamp when interview notification was last sent';

-- 2. Add interview_notified_by field  
-- Stores user ID who sent the interview notification
ALTER TABLE appx_application_details 
ADD COLUMN interview_notified_by INT(11) NULL 
COMMENT 'User ID who sent the interview notification';

-- 3. Add interview_notified_count field
-- Stores number of times interview notification email was sent
ALTER TABLE appx_application_details 
ADD COLUMN interview_notified_count INT(11) NULL DEFAULT 0 
COMMENT 'Number of times interview notification email was sent';

-- 4. Add interview_notified_email field
-- Stores email address where interview notification was sent
ALTER TABLE appx_application_details 
ADD COLUMN interview_notified_email VARCHAR(255) NULL 
COMMENT 'Email address where interview notification was sent';

-- 5. Add interview_notified_remarks field
-- Stores type of notification sent (scheduled/without_schedule)
ALTER TABLE appx_application_details 
ADD COLUMN interview_notified_remarks VARCHAR(50) NULL 
COMMENT 'Type of notification sent: scheduled or without_schedule';

-- =====================================================
-- PERFORMANCE INDEXES
-- =====================================================

-- Index on interview_notified_at for date-based queries
CREATE INDEX idx_interview_notified_at 
ON appx_application_details (interview_notified_at);

-- Index on interview_notified_by for user-based queries
CREATE INDEX idx_interview_notified_by 
ON appx_application_details (interview_notified_by);

-- Composite index for notification tracking queries
CREATE INDEX idx_interview_notification_tracking 
ON appx_application_details (interview_notified_count, interview_notified_at);

-- =====================================================
-- POST-MIGRATION VERIFICATION
-- =====================================================

-- Verify all fields were added successfully
SELECT 'Interview notification fields after migration:' as info;
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'dakoiim1_ders_db' 
  AND TABLE_NAME = 'appx_application_details' 
  AND COLUMN_NAME LIKE '%interview_notified%'
ORDER BY ORDINAL_POSITION;

-- Verify indexes were created
SELECT 'Indexes created for interview notification fields:' as info;
SHOW INDEX FROM appx_application_details WHERE Key_name LIKE '%interview%';

-- Test that new fields are queryable
SELECT 'Testing new fields functionality:' as info;
SELECT id, application_number, 
       interview_notified_at, 
       interview_notified_by, 
       interview_notified_count, 
       interview_notified_email, 
       interview_notified_remarks
FROM appx_application_details 
LIMIT 3;

-- Verify record count is unchanged
SELECT 'Total records after migration (should match before):' as info;
SELECT COUNT(*) as record_count FROM appx_application_details;

-- =====================================================
-- MIGRATION SUMMARY
-- =====================================================
SELECT '=== MIGRATION COMPLETED SUCCESSFULLY ===' as status;
SELECT 'Fields Added:' as summary;
SELECT '✅ interview_notified_at (DATETIME NULL)' as field_1;
SELECT '✅ interview_notified_by (INT(11) NULL)' as field_2;
SELECT '✅ interview_notified_count (INT(11) NULL DEFAULT 0)' as field_3;
SELECT '✅ interview_notified_email (VARCHAR(255) NULL)' as field_4;
SELECT '✅ interview_notified_remarks (VARCHAR(50) NULL)' as field_5;
SELECT 'Indexes Added:' as indexes;
SELECT '✅ idx_interview_notified_at' as index_1;
SELECT '✅ idx_interview_notified_by' as index_2;
SELECT '✅ idx_interview_notification_tracking' as index_3;

-- =====================================================
-- ROLLBACK STATEMENTS (FOR REFERENCE ONLY)
-- =====================================================
/*
-- EMERGENCY ROLLBACK - DO NOT EXECUTE UNLESS ROLLBACK IS NEEDED
-- Remove indexes first
DROP INDEX IF EXISTS idx_interview_notified_at ON appx_application_details;
DROP INDEX IF EXISTS idx_interview_notified_by ON appx_application_details;
DROP INDEX IF EXISTS idx_interview_notification_tracking ON appx_application_details;

-- Remove fields in reverse order
ALTER TABLE appx_application_details DROP COLUMN IF EXISTS interview_notified_remarks;
ALTER TABLE appx_application_details DROP COLUMN IF EXISTS interview_notified_email;
ALTER TABLE appx_application_details DROP COLUMN IF EXISTS interview_notified_count;
ALTER TABLE appx_application_details DROP COLUMN IF EXISTS interview_notified_by;
ALTER TABLE appx_application_details DROP COLUMN IF EXISTS interview_notified_at;
*/

-- =====================================================
-- DEPLOYMENT NOTES
-- =====================================================
/*
DEPLOYMENT INSTRUCTIONS:
1. Establish SSH tunnel: ssh -L 3307:127.0.0.1:3306 <EMAIL> -N
2. Create backup: mysqldump -h 127.0.0.1 -P 3307 -u dakoiim1_ders_db_admin -p dakoiim1_ders_db appx_application_details > backup.sql
3. Connect: mysql -h 127.0.0.1 -P 3307 -u dakoiim1_ders_db_admin -p
4. Execute: SOURCE /path/to/interview_feature_alter_statements.sql;
5. Verify results and test functionality

FEATURES ENABLED:
- Dual email notification types (scheduled/without_schedule)
- Modal interface for notification selection
- Real-time notification count tracking
- Email delivery tracking and audit trail
- Page position preservation after sending
- Enhanced visual feedback and animations

PRODUCTION TESTING:
- Navigate to: /interviews/schedule/1
- Test modal notification interface
- Send both notification types
- Verify database updates
- Check email delivery
*/
