<?php
/**
 * Interview Schedule - Generate and display interview schedule
 * Shows schedule based on selected positions and applicants
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<!-- DataTables CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css">
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-1">Interview Schedule</h4>
                    <p class="text-muted mb-0"><?= esc($interview['interview_title']) ?></p>
                </div>
                <div>
                    <a href="<?= base_url('interviews/dashboard/' . $interview['id']) ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Schedule Overview -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>Schedule Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">Interview Start Date</label>
                            <p class="form-control-plaintext">
                                <?= isset($settings['interview_start_date']) ? 
                                    date('M d, Y', strtotime($settings['interview_start_date'])) : 
                                    'Not set' ?>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">Interview Duration</label>
                            <p class="form-control-plaintext">
                                <?= $settings['interview_duration'] ?? 60 ?> minutes per interview
                            </p>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">Working Hours</label>
                            <p class="form-control-plaintext">
                                <?= $settings['start_time'] ?? '08:00' ?> - <?= $settings['end_time'] ?? '15:00' ?>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">Transition Period</label>
                            <p class="form-control-plaintext">
                                <?= $settings['transition_period'] ?? 10 ?> minutes between interviews
                            </p>
                        </div>
                        <div class="col-12">
                            <label class="form-label fw-bold">Shortlisted Applicants Breakdown</label>
                            <div class="row g-2">
                                <?php foreach ($interview_positions as $position): ?>
                                    <div class="col-md-4">
                                        <div class="border rounded p-2">
                                            <small class="text-muted d-block"><?= esc($position['designation']) ?></small>
                                            <strong class="text-primary"><?= $position_applicant_counts[$position['position_id']] ?? 0 ?> shortlisted</strong>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3 text-center">
                        <div class="col-6">
                            <div class="border rounded p-3">
                                <h4 class="text-primary mb-1"><?= count($interview_positions) ?></h4>
                                <small class="text-muted">Positions</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="border rounded p-3">
                                <h4 class="text-success mb-1"><?= $total_applicants ?></h4>
                                <small class="text-muted">Shortlisted</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Selected Positions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>Positions Selected for Interview
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($interview_positions)): ?>
                        <div class="text-center py-4">
                            <div class="mb-3">
                                <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                            </div>
                            <h5 class="text-warning">No Positions Selected</h5>
                            <p class="text-muted">You need to select positions before generating an interview schedule.</p>
                            <a href="<?= base_url('interviews/manage-positions/' . $interview['id']) ?>" class="btn btn-primary">
                                <i class="fas fa-users me-2"></i>Manage Positions
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Position</th>
                                        <th>Reference</th>
                                        <th>Group</th>
                                        <th>Priority</th>
                                        <th class="text-center">Shortlisted</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($interview_positions as $position): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <h6 class="mb-1"><?= esc($position['designation']) ?></h6>
                                                    <small class="text-muted"><?= esc($position['classification']) ?></small>
                                                </div>
                                            </td>
                                            <td><?= esc($position['position_reference']) ?></td>
                                            <td><?= esc($position['group_name']) ?></td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    <?= $position['priority'] ?: 'Not Set' ?>
                                                </span>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-info">
                                                    <?= $position_applicant_counts[$position['position_id']] ?? 0 ?>
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Interview Schedule Display -->
    <?php if ($can_generate_schedule && !empty($schedule)): ?>
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-calendar-check me-2"></i>Generated Interview Schedule
                        </h5>
                        <button class="btn btn-light btn-sm" onclick="exportSchedulePDF()">
                            <i class="fas fa-file-pdf me-1"></i>Export PDF
                        </button>
                    </div>
                    <div class="card-body">
                        <!-- Schedule Summary -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h6 class="fw-bold">Schedule Summary</h6>
                                <ul class="list-unstyled mb-0">
                                    <li><strong>Total Interviews:</strong> <?= count($schedule) ?></li>
                                    <li><strong>Interview Period:</strong>
                                        <?= date('M d, Y', strtotime($schedule[0]['date'])) ?> -
                                        <?= date('M d, Y', strtotime(end($schedule)['date'])) ?>
                                    </li>
                                    <li><strong>Duration per Interview:</strong> <?= $settings['interview_duration'] ?? 60 ?> minutes</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="fw-bold">Interview Settings</h6>
                                <ul class="list-unstyled mb-0">
                                    <li><strong>Working Hours:</strong> <?= $settings['start_time'] ?? '08:00' ?> - <?= $settings['end_time'] ?? '15:00' ?></li>
                                    <li><strong>Transition Period:</strong> <?= $settings['transition_period'] ?? 10 ?> minutes</li>
                                    <?php if (!empty($settings['venue'])): ?>
                                        <li><strong>Venue:</strong> <?= esc($settings['venue']) ?></li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        </div>

                        <!-- Schedule Table -->
                        <div class="table-responsive">
                            <table id="scheduleTable" class="table table-hover table-sm">
                                <thead class="table-light">
                                    <tr>
                                        <th>#</th>
                                        <th>Applicant Name</th>
                                        <th>Position Reference</th>
                                        <th>Position</th>
                                        <th>Date</th>
                                        <th>Time From - Time To</th>
                                        <th>Duration</th>
                                        <th>Priority</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($schedule as $slot): ?>
                                        <tr>
                                            <td>
                                                <span class="badge bg-secondary"><?= $slot['candidate_number'] ?></span>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?= esc($slot['applicant_name']) ?></strong>
                                                    <br><small class="text-muted"><?= esc($slot['application_number']) ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <code><?= esc($slot['position_reference']) ?></code>
                                            </td>
                                            <td>
                                                <div>
                                                    <h6 class="mb-1 small"><?= esc($slot['position_title']) ?></h6>
                                                    <small class="text-muted"><?= esc($slot['position']['classification']) ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?= date('M d, Y', strtotime($slot['date'])) ?></strong>
                                                    <br><small class="text-muted"><?= date('l', strtotime($slot['date'])) ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?= date('h:i A', strtotime($slot['start_time'])) ?> - <?= date('h:i A', strtotime($slot['end_time'])) ?></strong>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?= $slot['duration'] ?> min</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-warning text-dark">
                                                    <?= $slot['priority'] ?: 'N/A' ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php
                                                // Get notification count for this application from database
                                                $notificationCount = 0;
                                                $lastEmail = '';
                                                $lastNotificationType = '';

                                                // Fetch application details to get notification info
                                                $applicationDetailsModel = new \App\Models\AppxApplicationDetailsModel();
                                                $applicationDetails = $applicationDetailsModel->find($slot['application_id']);

                                                if ($applicationDetails) {
                                                    $notificationCount = (int)($applicationDetails['interview_notified_count'] ?? 0);
                                                    $lastEmail = $applicationDetails['interview_notified_email'] ?? '';
                                                    $lastNotificationType = $applicationDetails['interview_notified_remarks'] ?? '';
                                                }
                                                ?>
                                                <button type="button"
                                                        class="btn <?= $notificationCount > 0 ? 'btn-success' : 'btn-primary' ?> btn-sm notification-button"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#notificationModal"
                                                        data-application-id="<?= $slot['application_id'] ?>"
                                                        data-applicant-id="<?= $slot['applicant_id'] ?>"
                                                        data-slot-data="<?= htmlspecialchars(json_encode($slot), ENT_QUOTES, 'UTF-8') ?>"
                                                        data-applicant-name="<?= esc($slot['applicant_name']) ?>"
                                                        data-position="<?= esc($slot['position_title']) ?>"
                                                        title="Send Interview Notification<?= $notificationCount > 0 ? ' (Last: ' . $lastNotificationType . ')' : '' ?>">
                                                    <i class="fas fa-envelope me-1"></i>Send Notification
                                                    <span class="badge bg-light text-dark ms-1"><?= $notificationCount ?></span>
                                                    <?php if ($notificationCount > 0): ?>
                                                        <small class="d-block text-light" style="font-size: 0.7em;">
                                                            Last: <?= $lastNotificationType === 'scheduled' ? 'With Schedule' : ($lastNotificationType === 'without_schedule' ? 'Without Schedule' : 'N/A') ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php elseif (empty($interview_positions)): ?>
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>No Positions Selected
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center py-4">
                            <div class="mb-3">
                                <i class="fas fa-users text-warning" style="font-size: 3rem;"></i>
                            </div>
                            <h5 class="text-warning">No Positions Selected for Interview</h5>
                            <p class="text-muted">You need to select positions before viewing the interview schedule.</p>
                            <a href="<?= base_url('interviews/manage-positions/' . $interview['id']) ?>" class="btn btn-warning">
                                <i class="fas fa-users me-2"></i>Manage Positions
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>Missing Interview Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center py-4">
                            <div class="mb-3">
                                <i class="fas fa-cog text-warning" style="font-size: 3rem;"></i>
                            </div>
                            <h5 class="text-warning">Interview Settings Required</h5>
                            <p class="text-muted">
                                Please configure your interview settings to view the schedule.
                            </p>
                            <a href="<?= base_url('interviews/settings/' . $interview['id']) ?>" class="btn btn-warning">
                                <i class="fas fa-cog me-2"></i>Configure Settings
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable for Schedule if it exists
    if ($('#scheduleTable').length) {
        $('#scheduleTable').DataTable({
            "paging": true,
            "lengthChange": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "autoWidth": false,
            "responsive": true,
            "pageLength": 25,
            "language": {
                "search": "Search Schedule:",
                "emptyTable": "No interview schedule generated",
                "zeroRecords": "No matching interviews found"
            },
            "order": [[0, "asc"]], // Sort by candidate number
            "columnDefs": [
                { "orderable": true, "targets": "_all" },
                { "orderable": false, "targets": [8] } // Disable sorting for Action column
            ]
        });
    }
});

// PDF Export Function
function exportSchedulePDF() {
    const button = event.target.closest('button');
    const originalText = button.innerHTML;

    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Exporting...';
    button.disabled = true;

    // Create temporary form for direct download
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '<?= base_url('interviews/export-schedule/' . $interview['id']) ?>';
    form.target = '_blank'; // Open in new tab
    form.style.display = 'none';

    // Add CSRF token using CodeIgniter's csrf_field() helper
    form.innerHTML = '<?= csrf_field() ?>';

    // Submit form and cleanup
    document.body.appendChild(form);
    form.submit();

    setTimeout(() => {
        document.body.removeChild(form);
        button.innerHTML = originalText;
        button.disabled = false;
    }, 1000);
}
</script>

<style>
/* Enhanced notification button styles */
.notification-button {
    position: relative;
    transition: all 0.3s ease;
}

.notification-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.notification-button .badge {
    font-size: 0.7em;
    border-radius: 50%;
    min-width: 18px;
    height: 18px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Row highlight animation */
.table-success {
    background-color: #d1e7dd !important;
    animation: highlightFade 3s ease-in-out;
}

@keyframes highlightFade {
    0% { background-color: #198754 !important; }
    50% { background-color: #d1e7dd !important; }
    100% { background-color: transparent !important; }
}

/* Success notification animation */
.notification-sent {
    animation: successPulse 0.6s ease-in-out;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@media print {
    .btn, .card-header .btn, .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_length, .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_paginate {
        display: none !important;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
    }

    .table {
        font-size: 12px;
    }
}
</style>

<!-- Interview Notification Modal -->
<div class="modal fade" id="notificationModal" tabindex="-1" aria-labelledby="notificationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="notificationModalLabel">
                    <i class="fas fa-envelope text-primary me-2"></i>Send Interview Notification
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3">Applicant Information</h6>
                        <p><strong>Name:</strong> <span id="modal-applicant-name"></span></p>
                        <p><strong>Position:</strong> <span id="modal-position"></span></p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3">Notification History</h6>
                        <p><strong>Total Sent:</strong> <span id="modal-notification-count" class="badge bg-info">0</span></p>
                        <p><strong>Last Email:</strong> <span id="modal-last-email" class="text-muted">Not sent yet</span></p>
                        <p><strong>Last Type:</strong> <span id="modal-last-type" class="text-muted">N/A</span></p>
                    </div>
                </div>

                <hr>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-calendar-check text-success mb-3" style="font-size: 2rem;"></i>
                                <h6 class="card-title">With Schedule</h6>
                                <p class="card-text small text-muted">Send notification with specific interview date, time, and venue details.</p>
                                <button type="button" class="btn btn-success btn-sm" id="send-with-schedule">
                                    <i class="fas fa-calendar-plus me-1"></i>Send with Schedule
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-envelope text-warning mb-3" style="font-size: 2rem;"></i>
                                <h6 class="card-title">Without Schedule</h6>
                                <p class="card-text small text-muted">Send general interview invitation. Schedule details will be provided separately.</p>
                                <button type="button" class="btn btn-warning btn-sm" id="send-without-schedule">
                                    <i class="fas fa-envelope me-1"></i>Send without Schedule
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<script>
// Handle modal data population and form submission
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('notificationModal');
    let currentApplicationData = {};

    // Restore scroll position and highlight processed row after page load
    restorePagePosition();

    // When modal is triggered, populate with data
    modal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;

        // Extract data from button attributes
        currentApplicationData = {
            applicationId: button.getAttribute('data-application-id'),
            applicantId: button.getAttribute('data-applicant-id'),
            slotData: button.getAttribute('data-slot-data'),
            applicantName: button.getAttribute('data-applicant-name'),
            position: button.getAttribute('data-position')
        };

        // Populate modal content
        document.getElementById('modal-applicant-name').textContent = currentApplicationData.applicantName;
        document.getElementById('modal-position').textContent = currentApplicationData.position;

        // Fetch actual notification data via AJAX
        fetch(`<?= base_url('interviews/notification-data/') ?>${currentApplicationData.applicationId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('modal-notification-count').textContent = data.data.count;
                    document.getElementById('modal-last-email').textContent = data.data.last_email;

                    // Format notification type for display
                    let lastType = data.data.last_type;
                    if (lastType === 'scheduled') {
                        lastType = 'With Schedule';
                    } else if (lastType === 'without_schedule') {
                        lastType = 'Without Schedule';
                    }
                    document.getElementById('modal-last-type').textContent = lastType;
                } else {
                    console.error('Failed to fetch notification data:', data.message);
                    // Use fallback data
                    document.getElementById('modal-notification-count').textContent = '0';
                    document.getElementById('modal-last-email').textContent = 'Not sent yet';
                    document.getElementById('modal-last-type').textContent = 'N/A';
                }
            })
            .catch(error => {
                console.error('Error fetching notification data:', error);
                // Use fallback data
                document.getElementById('modal-notification-count').textContent = '0';
                document.getElementById('modal-last-email').textContent = 'Not sent yet';
                document.getElementById('modal-last-type').textContent = 'N/A';
            });
    });

    // Handle "Send with Schedule" button
    document.getElementById('send-with-schedule').addEventListener('click', function() {
        sendNotification('scheduled');
    });

    // Handle "Send without Schedule" button
    document.getElementById('send-without-schedule').addEventListener('click', function() {
        sendNotification('without_schedule');
    });

    function sendNotification(notificationType) {
        // Store current scroll position and application ID for restoration
        const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;
        const applicationId = currentApplicationData.applicationId;

        // Store position in sessionStorage for restoration after redirect
        sessionStorage.setItem('interview_scroll_position', scrollPosition);
        sessionStorage.setItem('interview_processed_application', applicationId);
        sessionStorage.setItem('interview_notification_type', notificationType);

        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?= base_url('interviews/send-notification/' . $interview['id']) ?>';
        form.style.display = 'none';

        // Add CSRF token
        form.innerHTML = '<?= csrf_field() ?>';

        // Create and append form fields safely
        const fields = [
            { name: 'application_id', value: currentApplicationData.applicationId },
            { name: 'applicant_id', value: currentApplicationData.applicantId },
            { name: 'slot_data', value: currentApplicationData.slotData },
            { name: 'notification_type', value: notificationType }
        ];

        fields.forEach(field => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = field.name;
            input.value = field.value;
            form.appendChild(input);
        });

        // Close modal before submitting
        const modal = bootstrap.Modal.getInstance(document.getElementById('notificationModal'));
        if (modal) {
            modal.hide();
        }

        document.body.appendChild(form);
        form.submit();
    }

    // Function to restore page position and highlight processed row
    function restorePagePosition() {
        const scrollPosition = sessionStorage.getItem('interview_scroll_position');
        const processedApplicationId = sessionStorage.getItem('interview_processed_application');
        const notificationType = sessionStorage.getItem('interview_notification_type');

        if (scrollPosition && processedApplicationId) {
            // Restore scroll position
            setTimeout(() => {
                window.scrollTo(0, parseInt(scrollPosition));

                // Find and highlight the processed row
                highlightProcessedRow(processedApplicationId, notificationType);

                // Clear stored data
                sessionStorage.removeItem('interview_scroll_position');
                sessionStorage.removeItem('interview_processed_application');
                sessionStorage.removeItem('interview_notification_type');
            }, 100); // Small delay to ensure page is fully loaded
        }
    }

    // Function to highlight the processed row
    function highlightProcessedRow(applicationId, notificationType) {
        // Find the button that was clicked (by application ID)
        const buttons = document.querySelectorAll('button[data-application-id="' + applicationId + '"]');

        if (buttons.length > 0) {
            const button = buttons[0];
            const row = button.closest('tr');

            if (row) {
                // Add highlight class
                row.classList.add('table-success');
                row.style.transition = 'background-color 0.3s ease';

                // Update button text to show updated count (this will be updated by page refresh)
                // Add a temporary success indicator
                const originalHtml = button.innerHTML;
                const typeText = notificationType === 'scheduled' ? 'with schedule' : 'without schedule';
                button.innerHTML = `<i class="fas fa-check text-success me-1"></i>Sent (${typeText})`;
                button.classList.remove('btn-primary');
                button.classList.add('btn-success');

                // Restore original appearance after 3 seconds
                setTimeout(() => {
                    button.innerHTML = originalHtml;
                    button.classList.remove('btn-success');
                    button.classList.add('btn-primary');
                    row.classList.remove('table-success');
                }, 3000);

                // Scroll to the row if it's not visible
                row.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }
    }
});
</script>

<?= $this->endSection() ?>
