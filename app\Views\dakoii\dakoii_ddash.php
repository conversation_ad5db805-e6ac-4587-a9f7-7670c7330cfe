<?= $this->extend("templates/dakoiiadmin"); ?>
<?= $this->section('content'); ?>

<div class="container-fluid py-4 px-4">
    <!-- Breadcrumbs -->
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb px-3 py-2 rounded" style="background-color: var(--lighter-bg);">
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>" class="text-light-text"><i class="fas fa-home me-1"></i>Dashboard</a></li>
            <li class="breadcrumb-item active" aria-current="page" style="color: var(--accent-color);">Overview</li>
        </ol>
    </nav>

    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h3 class="fw-bold text-light-text">Dashboard Overview</h3>
            <p class="text-secondary mb-0"><i class="fas fa-info-circle me-2"></i>Welcome to the Dakoii administration dashboard</p>
        </div>
        <div>
            <button class="btn btn-primary shadow-sm" data-bs-toggle="modal" data-bs-target="#modelId">
                <i class="fas fa-user-plus me-2"></i> Add System User
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4 g-3">
        <!-- Organizations Stats -->
        <div class="col-md-3">
            <div class="card shadow-sm border-0 h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="card-title mb-0 text-primary">Organizations</h6>
                            <h2 class="mt-2 mb-0 fw-bold text-light-text"><?= count($org) ?></h2>
                        </div>
                        <div class="rounded-circle bg-primary bg-opacity-10 p-3">
                            <i class="fas fa-building text-primary fa-2x"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="d-flex gap-2">
                            <span class="badge bg-lighter-bg text-light-text">
                                <i class="fas fa-check-circle me-1 text-success"></i>
                                Active: <?= count(array_filter($org, fn($o) => $o['is_active'] == 1)) ?>
                            </span>
                            <span class="badge bg-lighter-bg text-light-text">
                                <i class="fas fa-dollar-sign me-1 text-success"></i>
                                Paid: <?= count(array_filter($org, fn($o) => $o['license_status'] == 'paid')) ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Administrative Areas Stats -->
        <div class="col-md-3">
            <div class="card shadow-sm border-0 h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="card-title mb-0 text-success">Administrative Areas</h6>
                            <h2 class="mt-2 mb-0 fw-bold text-light-text"><?= $provinces_count ?></h2>
                        </div>
                        <div class="rounded-circle bg-success bg-opacity-10 p-3">
                            <i class="fas fa-map-marker-alt text-success fa-2x"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="d-flex gap-2 flex-wrap">
                            <span class="badge bg-lighter-bg text-light-text">
                                <i class="fas fa-map me-1 text-success"></i>
                                Districts: <?= $districts_count ?>
                            </span>
                            <span class="badge bg-lighter-bg text-light-text">
                                <i class="fas fa-landmark me-1 text-success"></i>
                                LLGs: <?= $llgs_count ?>
                            </span>
                            <span class="badge bg-lighter-bg text-light-text">
                                <i class="fas fa-home me-1 text-success"></i>
                                Wards: <?= $wards_count ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Users Stats -->
        <div class="col-md-3">
            <div class="card shadow-sm border-0 h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="card-title mb-0 text-info">System Users</h6>
                            <h2 class="mt-2 mb-0 fw-bold text-light-text"><?= count($dusers) ?></h2>
                        </div>
                        <div class="rounded-circle bg-info bg-opacity-10 p-3">
                            <i class="fas fa-users text-info fa-2x"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <span class="badge bg-lighter-bg text-light-text">
                            <i class="fas fa-check-circle me-1 text-info"></i>
                            Active: <?= count(array_filter($dusers, fn($u) => $u['is_active'] == 1)) ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Organization Admins Stats -->
        <div class="col-md-3">
            <div class="card shadow-sm border-0 h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="card-title mb-0 text-warning">Org Admins</h6>
                            <h2 class="mt-2 mb-0 fw-bold text-light-text"><?= count($admins) ?></h2>
                        </div>
                        <div class="rounded-circle bg-warning bg-opacity-10 p-3">
                            <i class="fas fa-user-shield text-warning fa-2x"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <span class="badge bg-lighter-bg text-light-text">
                            <i class="fas fa-users-cog me-1 text-warning"></i>
                            Total Organization Administrators
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Exercise Requests -->
    <?php if(isset($pending_exercises) && count($pending_exercises) > 0): ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-primary py-3">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-clipboard-list text-white me-2"></i>
                        <h5 class="fw-bold mb-0 text-white">Pending Exercise Publication Requests</h5>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover align-middle mb-0">
                            <thead class="bg-light">
                                <tr>
                                    <th class="ps-3 text-dark">#</th>
                                    <th class="text-dark">Exercise Name</th>
                                    <th class="text-dark">Organization</th>
                                    <th class="text-dark">Date Range</th>
                                    <th class="text-dark">Status</th>
                                    <th class="text-end pe-3 text-dark">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $i = 1; foreach ($pending_exercises as $exercise): ?>
                                <tr class="bg-light">
                                    <td class="ps-3 text-dark"><?= $i++ ?></td>
                                    <td class="fw-medium text-dark"><?= esc($exercise['exercise_name']) ?></td>
                                    <td class="text-dark">
                                        <?php if(isset($exercise['org_name'])): ?>
                                            <?= esc($exercise['org_name']) ?>
                                        <?php else: ?>
                                            <span class="text-muted fst-italic">Unknown</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-dark">
                                        <?php if(!empty($exercise['publish_date_from']) && !empty($exercise['publish_date_to'])): ?>
                                            <?= date('d M Y', strtotime($exercise['publish_date_from'])) ?> - 
                                            <?= date('d M Y', strtotime($exercise['publish_date_to'])) ?>
                                        <?php else: ?>
                                            <span class="text-muted fst-italic">Not set</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning text-dark">
                                            <i class="fas fa-clock me-1"></i> 
                                            <?= ucfirst($exercise['status']) ?>
                                        </span>
                                    </td>
                                    <td class="text-end pe-3">
                                        <button type="button" class="btn btn-sm btn-primary text-white change-status-btn" 
                                                data-id="<?= $exercise['id'] ?>"
                                                data-name="<?= esc($exercise['exercise_name']) ?>"
                                                data-bs-toggle="modal" 
                                                data-bs-target="#changeStatusModal<?= $exercise['id'] ?>">
                                            <i class="fas fa-edit me-1"></i> Change Status
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="row g-4">
        <!-- Organizations Table -->
        <div class="col-md-8">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-primary py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 text-white"><i class="fas fa-building me-2"></i>Recent Organizations</h5>
                        <a href="<?= base_url('dakoii/organization/list') ?>" class="btn btn-sm btn-light">
                            <i class="fas fa-list me-1"></i> View All
                        </a>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover align-middle mb-0">
                            <thead class="bg-light">
                                <tr>
                                    <th class="ps-3 text-dark">Name</th>
                                    <th class="text-dark">Code</th>
                                    <th class="text-dark">License</th>
                                    <th class="text-dark">Status</th>
                                    <th class="text-end pe-3 text-dark">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach (array_slice($org, 0, 5) as $o): ?>
                                <tr class="bg-light">
                                    <td class="ps-3">
                                        <div class="d-flex align-items-center">
                                            <img src="<?= imgcheck($o['logo_path']) ?>" alt="Logo" class="rounded-circle shadow-sm me-2" style="height: 36px; width: 36px; object-fit: cover; border: 2px solid var(--primary-color);">
                                            <span class="fw-medium text-dark"><?= esc($o['name']) ?></span>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-primary text-white px-2 py-1"><?= esc($o['orgcode']) ?></span></td>
                                    <td>
                                        <span class="badge bg-<?= $o['license_status'] == 'paid' ? 'success' : 'warning' ?> <?= $o['license_status'] == 'paid' ? 'text-white' : 'text-dark' ?>">
                                            <i class="fas fa-<?= $o['license_status'] == 'paid' ? 'check-circle' : 'exclamation-circle' ?> me-1"></i>
                                            <?= ucfirst($o['license_status']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $o['is_active'] ? 'success' : 'danger' ?> text-white">
                                            <i class="fas fa-<?= $o['is_active'] ? 'check-circle' : 'times-circle' ?> me-1"></i>
                                            <?= $o['is_active'] ? 'Active' : 'Inactive' ?>
                                        </span>
                                    </td>
                                    <td class="text-end pe-3">
                                        <a href="<?= base_url('dakoii/organization/view/' . $o['orgcode']) ?>" 
                                           class="btn btn-sm btn-primary text-white">
                                            <i class="fas fa-eye me-1"></i> View
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Users Table -->
        <div class="col-md-4">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-info py-3">
                    <h5 class="mb-0 text-white"><i class="fas fa-users me-2"></i>System Users</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover align-middle mb-0">
                            <thead class="bg-light">
                                <tr>
                                    <th class="ps-3 text-dark">Name</th>
                                    <th class="text-dark">Role</th>
                                    <th class="text-dark">Status</th>
                                    <th class="text-end pe-3 text-dark"></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($dusers as $user): ?>
                                <tr class="bg-light">
                                    <td class="ps-3 fw-medium text-dark"><?= esc($user['name']) ?></td>
                                    <td>
                                        <span class="badge bg-info text-white">
                                            <i class="fas fa-user-tag me-1"></i>
                                            <?= ucfirst($user['role']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $user['is_active'] ? 'success' : 'danger' ?> text-white">
                                            <i class="fas fa-<?= $user['is_active'] ? 'check-circle' : 'times-circle' ?> me-1"></i>
                                            <?= $user['is_active'] ? 'Active' : 'Inactive' ?>
                                        </span>
                                    </td>
                                    <td class="text-end pe-3">
                                        <button class="btn btn-sm btn-primary text-white edit-system-user" 
                                                data-id="<?= $user['id'] ?>"
                                                data-name="<?= esc($user['name']) ?>"
                                                data-username="<?= esc($user['username']) ?>"
                                                data-role="<?= esc($user['role']) ?>"
                                                data-active="<?= $user['is_active'] ?>"
                                                data-bs-toggle="modal" 
                                                data-bs-target="#editSystemUserModal">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add System User Modal -->
<div class="modal fade" id="modelId" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title"><i class="fas fa-user-plus me-2"></i>Add System User</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?= form_open('dakoii/system-user/create') ?>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="name" class="form-label">Full Name</label>
                    <input type="text" class="form-control" id="name" name="name" required>
                </div>
                <div class="mb-3">
                    <label for="username" class="form-label">Username</label>
                    <input type="text" class="form-control" id="username" name="username" required>
                </div>
                <div class="mb-3">
                    <label for="password" class="form-label">Password</label>
                    <input type="password" class="form-control" id="password" name="password" required>
                </div>
                <div class="mb-3">
                    <label for="role" class="form-label">Role</label>
                    <select class="form-select" id="role" name="role" required>
                        <option value="admin">Admin</option>
                        <option value="manager">Manager</option>
                    </select>
                </div>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" checked>
                    <label class="form-check-label" for="is_active">Active Account</label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary">Save User</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<!-- Edit System User Modal -->
<div class="modal fade" id="editSystemUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title"><i class="fas fa-user-edit me-2"></i>Edit System User</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?= form_open('dakoii/system-user/update') ?>
            <div class="modal-body">
                <input type="hidden" id="edit_id" name="id">
                
                <div class="mb-3">
                    <label for="edit_name" class="form-label">Full Name</label>
                    <input type="text" class="form-control" id="edit_name" name="name" required>
                </div>
                <div class="mb-3">
                    <label for="edit_username" class="form-label">Username</label>
                    <input type="text" class="form-control" id="edit_username" name="username" required>
                </div>
                <div class="mb-3">
                    <label for="edit_password" class="form-label">Password (leave blank to keep current)</label>
                    <input type="password" class="form-control" id="edit_password" name="password">
                </div>
                <div class="mb-3">
                    <label for="edit_role" class="form-label">Role</label>
                    <select class="form-select" id="edit_role" name="role" required>
                        <option value="admin">Admin</option>
                        <option value="manager">Manager</option>
                    </select>
                </div>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active" value="1">
                    <label class="form-check-label" for="edit_is_active">Active Account</label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary">Update User</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<!-- Exercise Status Change Modals -->
<?php if(isset($pending_exercises)): ?>
    <?php foreach ($pending_exercises as $exercise): ?>
    <div class="modal fade" id="changeStatusModal<?= $exercise['id'] ?>" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content bg-dark-bg">
                <div class="modal-header bg-primary">
                    <h5 class="modal-title text-white">
                        <i class="fas fa-edit me-2"></i> Change Exercise Status
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <?= form_open('dakoii/exercise/change-status/' . $exercise['id']) ?>
                <div class="modal-body p-4">
                    <!-- Add CSRF token -->
                    <?= csrf_field() ?>
                    
                    <div class="text-center mb-4">
                        <div class="d-inline-block p-3 rounded-circle bg-primary bg-opacity-25 mb-3">
                            <i class="fas fa-clipboard-list text-primary" style="font-size: 2rem;"></i>
                        </div>
                        <h5 class="text-light-text"><?= esc($exercise['exercise_name']) ?></h5>
                        <p class="text-secondary">Current Status: <span class="badge bg-warning text-dark"><?= ucfirst(str_replace('_', ' ', $exercise['status'])) ?></span></p>
                    </div>
                    
                    <div class="mb-3">
                        <label for="status_<?= $exercise['id'] ?>" class="form-label text-light-text">New Status</label>
                        <select class="form-select form-select-lg" name="status" id="status_<?= $exercise['id'] ?>" required>
                            <option value="publish">Publish</option>
                            <option value="draft">Draft</option>
                            <option value="selection">Selection</option>
                            <option value="review">Review</option>
                            <option value="closed">Closed</option>
                        </select>
                        <div class="form-text text-secondary mt-2">
                            <ul class="ps-3 mb-0">
                                <li><b>Publish</b>: Make exercise publicly visible</li>
                                <li><b>Draft</b>: Save as work in progress</li>
                                <li><b>Selection</b>: Move to selection phase</li>
                                <li><b>Review</b>: Mark for review</li>
                                <li><b>Closed</b>: Exercise is closed and archived</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary text-light-text" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i> Cancel
                    </button>
                    <button type="submit" class="btn btn-primary text-white">
                        <i class="fas fa-save me-1"></i> Update Status
                    </button>
                </div>
                <?= form_close() ?>
            </div>
        </div>
    </div>
    <?php endforeach; ?>
<?php endif; ?>

<script>
    // Set up the edit user modal
    document.addEventListener('DOMContentLoaded', function() {
        var editSystemUserModal = document.getElementById('editSystemUserModal');
        if (editSystemUserModal) {
            editSystemUserModal.addEventListener('show.bs.modal', function(event) {
                var button = event.relatedTarget;
                
                // Extract info from data attributes
                var id = button.getAttribute('data-id');
                var name = button.getAttribute('data-name');
                var username = button.getAttribute('data-username');
                var role = button.getAttribute('data-role');
                var active = button.getAttribute('data-active');
                
                // Update the modal's content
                var modalId = editSystemUserModal.querySelector('#edit_id');
                var modalName = editSystemUserModal.querySelector('#edit_name');
                var modalUsername = editSystemUserModal.querySelector('#edit_username');
                var modalRole = editSystemUserModal.querySelector('#edit_role');
                var modalActive = editSystemUserModal.querySelector('#edit_is_active');
                
                modalId.value = id;
                modalName.value = name;
                modalUsername.value = username;
                modalRole.value = role;
                modalActive.checked = active === '1';
            });
        }
    });
</script>

<?= $this->endSection() ?>
