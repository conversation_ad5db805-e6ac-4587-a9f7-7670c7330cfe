<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="card mb-4 border-start border-primary border-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h4 fw-bold text-gray-800 mb-1">Applications Notifications - Send Notifications</h2>
                    <p class="text-muted mb-0">Send shortlisting result notifications to applicants</p>
                </div>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="<?= base_url('applications/notifications') ?>" class="text-decoration-none">Exercises</a></li>
                        <li class="breadcrumb-item">
                            <a href="<?= base_url('applications/notifications/position-groups/' . $position['exercise_id']) ?>" class="text-decoration-none">
                                Position Groups
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?= base_url('applications/notifications/positions/' . $position['position_group_id']) ?>" class="text-decoration-none">
                                Positions
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">Applications</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Position Information -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <h5 class="card-title mb-3">
                        <i class="fas fa-briefcase me-2 text-primary"></i>
                        <?= esc($position['designation']) ?>
                    </h5>
                    <div class="row">
                        <div class="col-sm-6">
                            <p class="mb-1"><strong>Position Reference:</strong> <?= esc($position['position_reference']) ?></p>
                            <p class="mb-1"><strong>Classification:</strong> <?= esc($position['classification']) ?></p>
                            <p class="mb-1"><strong>Location:</strong> <?= esc($position['location']) ?></p>
                        </div>
                        <div class="col-sm-6">
                            <p class="mb-1"><strong>Exercise:</strong> <?= esc($position['exercise_name']) ?></p>
                            <p class="mb-1"><strong>Advertisement No:</strong> <?= esc($position['advertisement_no']) ?></p>
                            <p class="mb-1"><strong>Organization:</strong> <?= esc($position['org_name']) ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <a href="<?= base_url('applications/notifications/positions/' . $position['position_group_id']) ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Back to Positions
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('warning')): ?>
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?= session()->getFlashdata('warning') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Applications List -->
    <div class="card">
        <div class="card-body">
            <?php if (empty($applications)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Applications Available</h5>
                    <p class="text-muted">There are no applications that have been shortlisted or eliminated for this position.</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Application Details</th>
                                <th>Applicant Name</th>
                                <th class="text-center">Rating Score</th>
                                <th class="text-center">Shortlist Status</th>
                                <th class="text-center">Notification Status</th>
                                <th class="text-center">Sent By</th>
                                <th class="text-center">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($applications as $application): ?>
                                <tr>
                                    <td>
                                        <div class="fw-bold"><?= esc($application['application_number']) ?></div>
                                        <small class="text-muted">Applied: <?= date('M d, Y', strtotime($application['created_at'])) ?></small>
                                    </td>
                                    <td>
                                        <div><?= esc($application['first_name'] . ' ' . $application['last_name']) ?></div>
                                        <small class="text-muted"><?= esc($application['email_address']) ?></small>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-info"><?= number_format($application['rating_capability_max'], 1) ?></span>
                                    </td>
                                    <td class="text-center">
                                        <?php if ($application['shortlist_status'] === 'shortlisted'): ?>
                                            <span class="badge bg-success">Shortlisted</span>
                                        <?php elseif ($application['shortlist_status'] === 'eliminated'): ?>
                                            <span class="badge bg-danger">Eliminated</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <?php if (!empty($application['shortlisting_notified_status']) && !empty($application['shortlisting_notified_by'])): ?>
                                            <span class="badge bg-secondary">
                                                <?= ucfirst($application['shortlisting_notified_status']) ?> Sent
                                            </span>
                                            <br>
                                            <?php if (!empty($application['shortlisting_notified_at'])): ?>
                                                <small class="text-muted"><?= date('M d, Y H:i', strtotime($application['shortlisting_notified_at'])) ?></small>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="badge bg-warning">Not Sent</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <?php if (!empty($application['notified_by_name'])): ?>
                                            <span class="text-muted"><?= esc($application['notified_by_name']) ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <?php if (empty($application['shortlisting_notified_status']) && empty($application['shortlisting_notified_by'])): ?>
                                            <?php if ($application['shortlist_status'] === 'shortlisted'): ?>
                                                <form method="POST" action="<?= base_url('applications/notifications/send-successful/' . $application['id']) ?>" style="display: inline;">
                                                    <?= csrf_field() ?>
                                                    <button type="submit" class="btn btn-success btn-sm"
                                                            onclick="return confirm('Send successful shortlisting notification to <?= esc($application['first_name'] . ' ' . $application['last_name']) ?>?')">
                                                        <i class="fas fa-check me-1"></i> Send Successful
                                                    </button>
                                                </form>
                                            <?php elseif ($application['shortlist_status'] === 'eliminated'): ?>
                                                <form method="POST" action="<?= base_url('applications/notifications/send-unsuccessful/' . $application['id']) ?>" style="display: inline;">
                                                    <?= csrf_field() ?>
                                                    <button type="submit" class="btn btn-danger btn-sm"
                                                            onclick="return confirm('Send unsuccessful shortlisting notification to <?= esc($application['first_name'] . ' ' . $application['last_name']) ?>?')">
                                                        <i class="fas fa-times me-1"></i> Send Unsuccessful
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="text-muted">Already Sent</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Summary Information -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">Summary</h6>
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>Total Applications:</strong> <?= count($applications) ?>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Shortlisted:</strong> 
                                        <?= count(array_filter($applications, function($app) { return $app['shortlist_status'] === 'shortlisted'; })) ?>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Eliminated:</strong> 
                                        <?= count(array_filter($applications, function($app) { return $app['shortlist_status'] === 'eliminated'; })) ?>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Notifications Sent:</strong>
                                        <?= count(array_filter($applications, function($app) { return !empty($app['shortlisting_notified_status']) && !empty($app['shortlisting_notified_by']); })) ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
