<?php

namespace App\Controllers;

use CodeIgniter\API\ResponseTrait;

class AcknowledgedApplicationsController extends BaseController
{
    use ResponseTrait;

    protected $session;
    protected $applicationsModel;

    public function __construct()
    {
        helper(['form', 'url']);
        $this->session = \Config\Services::session();
        $this->applicationsModel = new \App\Models\AppxApplicationDetailsModel();
    }

    /**
     * Display the list of acknowledged applications
     */
    public function index()
    {
        // Get current user's organization ID from session
        $orgId = session()->get('org_id');

        // Get acknowledged applications data from database using model method, filtered by organization
        $applications = $this->applicationsModel->getAcknowledgedApplicationsWithDetails($orgId);

        // Process applications for display compatibility
        foreach ($applications as &$application) {
            // Convert JSON fields if they exist
            if (!empty($application['contact_details']) && is_string($application['contact_details'])) {
                $contactData = json_decode($application['contact_details'], true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($contactData)) {
                    $application['contact_details'] = $contactData;
                }
            }

            if (!empty($application['id_numbers']) && is_string($application['id_numbers'])) {
                $idData = json_decode($application['id_numbers'], true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($idData)) {
                    $application['id_numbers'] = $idData;
                }
            }

            if (!empty($application['location_address']) && is_string($application['location_address'])) {
                $locationData = json_decode($application['location_address'], true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($locationData)) {
                    $application['location_address'] = $locationData;
                }
            }

            if (!empty($application['referees']) && is_string($application['referees'])) {
                $refereesData = json_decode($application['referees'], true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($refereesData)) {
                    $application['referees'] = $refereesData;
                }
            }

            // Set display names for compatibility with view
            $application['fname'] = $application['first_name'];
            $application['lname'] = $application['last_name'];
            $application['recieved_acknowledged'] = $application['received_at'];

            // Set department from organization name if not available
            $application['department'] = $application['org_name'] ?? 'N/A';
        }

        $data = [
            'title' => 'Acknowledged Applications',
            'menu' => 'applications',
            'applications' => $applications
        ];

        return view('acknowledged_applications/acknowledged_applications_index', $data);
    }

    /**
     * View a specific acknowledged application
     */
    public function view($id)
    {
        // Get current user's organization ID from session
        $orgId = session()->get('org_id');

        // Get application details with full data, filtered by organization
        $application = $this->applicationsModel->getApplicationWithDetails($id, $orgId);

        if (!$application) {
            session()->setFlashdata('error', 'Application not found or access denied');
            return redirect()->to('acknowledged_applications');
        }

        // Check if application is actually acknowledged
        if ($application['is_received'] != 1) {
            session()->setFlashdata('error', 'This application has not been acknowledged yet');
            return redirect()->to('acknowledged_applications');
        }

        // Process JSON fields for display
        $jsonFields = ['contact_details', 'id_numbers', 'location_address', 'referees'];
        foreach ($jsonFields as $field) {
            if (!empty($application[$field]) && is_string($application[$field])) {
                $decoded = json_decode($application[$field], true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                    $application[$field] = $decoded;
                }
            }
        }

        // Get application files
        $filesModel = new \App\Models\AppxApplicationFilesModel();
        $files = $filesModel->getFilesByApplicationId($id);

        // Get application experiences
        $experiencesModel = new \App\Models\AppxApplicationExperiencesModel();
        $experiences = $experiencesModel->getExperiencesByApplicationId($id);

        // Get application education
        $educationModel = new \App\Models\AppxApplicationEducationModel();
        $education = $educationModel->getEducationByApplicationId($id);

        $data = [
            'title' => 'View Acknowledged Application',
            'menu' => 'applications',
            'application' => $application,
            'files' => $files,
            'experiences' => $experiences,
            'education' => $education
        ];

        return view('acknowledged_applications/acknowledged_applications_view', $data);
    }
}
