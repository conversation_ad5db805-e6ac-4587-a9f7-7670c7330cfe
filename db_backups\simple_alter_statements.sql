-- =====================================================
-- Interview Feature - Simple ALTER TABLE Statements
-- =====================================================
-- Execute these statements to add all interview notification fields
-- Target: appx_application_details table

-- Use production database
USE dakoiim1_ders_db;

-- Add all interview notification fields
ALTER TABLE appx_application_details 
ADD COLUMN interview_notified_at DATETIME NULL 
COMMENT 'Timestamp when interview notification was last sent';

ALTER TABLE appx_application_details 
ADD COLUMN interview_notified_by INT(11) NULL 
COMMENT 'User ID who sent the interview notification';

ALTER TABLE appx_application_details 
ADD COLUMN interview_notified_count INT(11) NULL DEFAULT 0 
COMMENT 'Number of times interview notification email was sent';

ALTER TABLE appx_application_details 
ADD COLUMN interview_notified_email VARCHAR(255) NULL 
COMMENT 'Email address where interview notification was sent';

ALTER TABLE appx_application_details 
ADD COLUMN interview_notified_remarks VARCHAR(50) NULL 
COMMENT 'Type of notification sent: scheduled or without_schedule';

-- Add performance indexes
CREATE INDEX idx_interview_notified_at ON appx_application_details (interview_notified_at);
CREATE INDEX idx_interview_notified_by ON appx_application_details (interview_notified_by);
CREATE INDEX idx_interview_notification_tracking ON appx_application_details (interview_notified_count, interview_notified_at);

-- Verify changes
DESCRIBE appx_application_details;
