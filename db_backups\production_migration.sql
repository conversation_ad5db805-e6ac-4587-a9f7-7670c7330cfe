-- Production Database Migration Script
-- Interview Notification System Fields
-- Target Database: dakoiim1_ders_db
-- Date: 2025-01-29
-- 
-- This script adds all interview notification fields to the production database
-- Execute this after establishing SSH tunnel and creating backup

-- Pre-migration verification
SELECT 'Starting Interview Notification Migration' as status;
SELECT NOW() as migration_start_time;

-- Check current table structure
SELECT 'Current table structure:' as info;
DESCRIBE appx_application_details;

-- Check if interview notification fields already exist
SELECT 'Checking for existing interview notification fields:' as info;
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'dakoiim1_ders_db' 
  AND TABLE_NAME = 'appx_application_details' 
  AND COLUMN_NAME LIKE '%interview_notified%'
ORDER BY ORDINAL_POSITION;

-- Count total records before migration
SELECT 'Total records before migration:' as info;
SELECT COUNT(*) as total_records FROM appx_application_details;

-- Begin migration
SELECT 'Beginning migration...' as status;

-- Add interview_notified_at field
SELECT 'Adding interview_notified_at field...' as status;
ALTER TABLE appx_application_details 
ADD COLUMN interview_notified_at DATETIME NULL 
COMMENT 'Timestamp when interview notification was last sent';

-- Add interview_notified_by field  
SELECT 'Adding interview_notified_by field...' as status;
ALTER TABLE appx_application_details 
ADD COLUMN interview_notified_by INT(11) NULL 
COMMENT 'User ID who sent the interview notification';

-- Add interview_notified_count field
SELECT 'Adding interview_notified_count field...' as status;
ALTER TABLE appx_application_details 
ADD COLUMN interview_notified_count INT(11) NULL DEFAULT 0 
COMMENT 'Number of times interview notification email was sent';

-- Add interview_notified_email field
SELECT 'Adding interview_notified_email field...' as status;
ALTER TABLE appx_application_details 
ADD COLUMN interview_notified_email VARCHAR(255) NULL 
COMMENT 'Email address where interview notification was sent';

-- Add interview_notified_remarks field
SELECT 'Adding interview_notified_remarks field...' as status;
ALTER TABLE appx_application_details 
ADD COLUMN interview_notified_remarks VARCHAR(50) NULL 
COMMENT 'Type of notification sent: scheduled or without_schedule';

-- Add performance indexes
SELECT 'Adding performance indexes...' as status;

-- Index on interview_notified_at for date-based queries
CREATE INDEX idx_interview_notified_at 
ON appx_application_details (interview_notified_at);

-- Index on interview_notified_by for user-based queries
CREATE INDEX idx_interview_notified_by 
ON appx_application_details (interview_notified_by);

-- Composite index for notification tracking queries
CREATE INDEX idx_interview_notification_tracking 
ON appx_application_details (interview_notified_count, interview_notified_at);

-- Post-migration verification
SELECT 'Migration completed. Verifying results...' as status;

-- Check new table structure
SELECT 'New table structure with interview notification fields:' as info;
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'dakoiim1_ders_db' 
  AND TABLE_NAME = 'appx_application_details' 
  AND COLUMN_NAME LIKE '%interview_notified%'
ORDER BY ORDINAL_POSITION;

-- Verify indexes were created
SELECT 'Checking indexes:' as info;
SHOW INDEX FROM appx_application_details WHERE Key_name LIKE '%interview%';

-- Count total records after migration (should be same)
SELECT 'Total records after migration:' as info;
SELECT COUNT(*) as total_records FROM appx_application_details;

-- Test that new fields are queryable
SELECT 'Testing new fields functionality:' as info;
SELECT id, application_number, 
       interview_notified_at, 
       interview_notified_by, 
       interview_notified_count, 
       interview_notified_email, 
       interview_notified_remarks
FROM appx_application_details 
LIMIT 3;

-- Final status
SELECT 'MIGRATION COMPLETED SUCCESSFULLY' as final_status;
SELECT NOW() as migration_end_time;

-- Summary of changes
SELECT 'SUMMARY OF CHANGES:' as summary;
SELECT '✅ Added interview_notified_at DATETIME NULL' as change_1;
SELECT '✅ Added interview_notified_by INT(11) NULL' as change_2;
SELECT '✅ Added interview_notified_count INT(11) NULL DEFAULT 0' as change_3;
SELECT '✅ Added interview_notified_email VARCHAR(255) NULL' as change_4;
SELECT '✅ Added interview_notified_remarks VARCHAR(50) NULL' as change_5;
SELECT '✅ Added 3 performance indexes' as change_6;
SELECT '✅ All fields are queryable and functional' as change_7;

-- Rollback script (for reference, DO NOT EXECUTE unless needed)
/*
-- ROLLBACK SCRIPT (Execute only if rollback is needed)
DROP INDEX IF EXISTS idx_interview_notified_at ON appx_application_details;
DROP INDEX IF EXISTS idx_interview_notified_by ON appx_application_details;
DROP INDEX IF EXISTS idx_interview_notification_tracking ON appx_application_details;

ALTER TABLE appx_application_details DROP COLUMN IF EXISTS interview_notified_remarks;
ALTER TABLE appx_application_details DROP COLUMN IF EXISTS interview_notified_email;
ALTER TABLE appx_application_details DROP COLUMN IF EXISTS interview_notified_count;
ALTER TABLE appx_application_details DROP COLUMN IF EXISTS interview_notified_by;
ALTER TABLE appx_application_details DROP COLUMN IF EXISTS interview_notified_at;
*/
