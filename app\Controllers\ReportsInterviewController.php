<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use App\Models\ExerciseModel;
use App\Models\AppxApplicationDetailsModel;
use App\Models\InterviewsModel;
use App\Models\InterviewPositionsModel;

/**
 * ReportsInterviewController
 *
 * Dedicated controller for interview-related reports and schedule management
 */
class ReportsInterviewController extends Controller
{
    protected $exerciseModel;
    protected $applicationModel;
    protected $interviewsModel;
    protected $interviewPositionsModel;

    public function __construct()
    {
        helper(['url', 'form']);
        $this->exerciseModel = new ExerciseModel();
        $this->applicationModel = new AppxApplicationDetailsModel();
        $this->interviewsModel = new InterviewsModel();
        $this->interviewPositionsModel = new InterviewPositionsModel();
    }

    /**
     * [GET] Display interview schedule for specific exercise
     * URI: /reports/interview-schedule/{exerciseId}
     */
    public function schedule($exerciseId)
    {
        try {
            // Get exercise from this organization only
            $orgId = session()->get('org_id');
            $exercise = $this->exerciseModel->where('id', $exerciseId)->where('org_id', $orgId)->first();
            if (!$exercise) {
                return redirect()->to('reports/exercises')
                                ->with('error', 'Exercise not found');
            }

            // Get interview for this exercise with details
            $interviewRecord = $this->interviewsModel->where('exercise_id', $exerciseId)->where('org_id', $orgId)->first();
            if (!$interviewRecord) {
                return redirect()->to('reports/dashboard/' . $exerciseId)
                                ->with('error', 'No interview found for this exercise');
            }

            // Get interview with full details using the same method as local version
            $interview = $this->interviewsModel->getInterviewWithDetails($interviewRecord['id']);
            if (!$interview) {
                return redirect()->to('reports/dashboard/' . $exerciseId)
                                ->with('error', 'Interview details not found');
            }

            // Get interview positions with details
            $interviewPositions = $this->interviewPositionsModel->getInterviewPositionsWithDetails($exerciseId);

            // Get interview settings
            $settings = json_decode($interview['interview_settings'], true) ?? [];

            // Check if we can generate a schedule
            $canGenerateSchedule = !empty($settings['interview_start_date']) && 
                                  !empty($settings['interview_duration']) && 
                                  !empty($interviewPositions);

            $schedule = [];
            $totalApplicants = 0;
            $positionApplicantCounts = [];
            
            if ($canGenerateSchedule) {
                // Sort positions by priority (higher priority first, null priority last)
                usort($interviewPositions, function($a, $b) {
                    $priorityA = $a['priority'] ?? 999;
                    $priorityB = $b['priority'] ?? 999;
                    return $priorityA <=> $priorityB;
                });

                // Generate the schedule automatically
                $schedule = $this->buildInterviewSchedule($interview, $settings, $interviewPositions);
                
                // Count total applicants from selected positions
                foreach ($interviewPositions as $position) {
                    $count = $this->getApplicantsCountForPosition($interview['org_id'], $interview['exercise_id'], $position['position_id']);
                    $totalApplicants += $count;
                    $positionApplicantCounts[$position['position_id']] = $count;
                }
            } else {
                // Still count applicants for display even if we can't generate schedule
                foreach ($interviewPositions as $position) {
                    $count = $this->getApplicantsCountForPosition($interview['org_id'], $interview['exercise_id'], $position['position_id']);
                    $totalApplicants += $count;
                    $positionApplicantCounts[$position['position_id']] = $count;
                }
            }

            $data = [
                'title' => 'Interview Schedule - ' . $exercise['exercise_name'],
                'menu' => 'reports',
                'exercise' => $exercise,
                'interview' => $interview,
                'interview_positions' => $interviewPositions,
                'settings' => $settings,
                'total_applicants' => $totalApplicants,
                'position_applicant_counts' => $positionApplicantCounts,
                'schedule' => $schedule,
                'can_generate_schedule' => $canGenerateSchedule
            ];

            return view('application_reports/appx_reports_interview_schedule', $data);
        } catch (\Exception $e) {
            log_message('error', 'Error fetching interview schedule data: ' . $e->getMessage());
            return redirect()->to('reports/exercises')
                            ->with('error', 'Error loading interview schedule');
        }
    }

    /**
     * Export Interview Schedule as PDF
     */
    public function exportPDF($exerciseId)
    {
        // Validate exercise ID
        if (!$exerciseId) {
            $this->response->setContentType('application/json');
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Exercise ID is required'
            ]);
        }

        try {
            // Get exercise from this organization only
            $orgId = session()->get('org_id');
            $exercise = $this->exerciseModel->where('id', $exerciseId)->where('org_id', $orgId)->first();
            if (!$exercise) {
                $this->response->setContentType('application/json');
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Exercise not found'
                ]);
            }

            // Get interview for this exercise with details
            $interviewRecord = $this->interviewsModel->where('exercise_id', $exerciseId)->where('org_id', $orgId)->first();
            if (!$interviewRecord) {
                $this->response->setContentType('application/json');
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'No interview found for this exercise'
                ]);
            }

            // Get interview with full details using the same method as local version
            $interview = $this->interviewsModel->getInterviewWithDetails($interviewRecord['id']);
            if (!$interview) {
                $this->response->setContentType('application/json');
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Interview details not found'
                ]);
            }

            // Get interview positions with details
            $interviewPositions = $this->interviewPositionsModel->getInterviewPositionsWithDetails($exerciseId);

            // Get interview settings
            $settings = json_decode($interview['interview_settings'], true) ?? [];

            // Validate required settings
            if (empty($settings['interview_start_date']) || empty($settings['interview_duration'])) {
                $this->response->setContentType('application/json');
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Please configure interview settings before exporting schedule'
                ]);
            }

            // Sort positions by priority (higher priority first, null priority last)
            usort($interviewPositions, function($a, $b) {
                $priorityA = $a['priority'] ?? 999;
                $priorityB = $b['priority'] ?? 999;
                return $priorityA <=> $priorityB;
            });

            if (empty($interviewPositions)) {
                $this->response->setContentType('application/json');
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Please select positions for interview before exporting schedule'
                ]);
            }

            // Generate the schedule
            $schedule = $this->buildInterviewSchedule($interview, $settings, $interviewPositions);

            if (empty($schedule)) {
                $this->response->setContentType('application/json');
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'No interview schedule could be generated. Please check your settings.'
                ]);
            }

            // Generate and output PDF directly to browser
            $this->generateInterviewSchedulePDF($exercise, $interview, $settings, $schedule);

            // No return needed - PDF is sent directly to browser

        } catch (\Exception $e) {
            log_message('error', 'Interview Schedule PDF Export Error: ' . $e->getMessage());
            $this->response->setContentType('application/json');
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to generate PDF: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get shortlisted applicants count for a position in a specific exercise and organization
     */
    private function getApplicantsCountForPosition($orgId, $exerciseId, $positionId)
    {
        return $this->applicationModel
            ->where('org_id', $orgId)
            ->where('exercise_id', $exerciseId)
            ->where('position_id', $positionId)
            ->where('shortlist_status', 'shortlisted') // Only count shortlisted applicants
            ->where('deleted_at', null) // Only count non-deleted applications
            ->countAllResults();
    }

    /**
     * Get actual shortlisted applicants for a position in a specific exercise and organization
     */
    private function getShortlistedApplicantsForPosition($orgId, $exerciseId, $positionId)
    {
        return $this->applicationModel
            ->select('applicant_id, application_number, first_name, last_name, email_address')
            ->where('org_id', $orgId)
            ->where('exercise_id', $exerciseId)
            ->where('position_id', $positionId)
            ->where('shortlist_status', 'shortlisted') // Only get shortlisted applicants
            ->where('deleted_at', null) // Only get non-deleted applications
            ->orderBy('application_number', 'ASC') // Order by application number
            ->findAll();
    }

    /**
     * Build the interview schedule based on settings and positions
     */
    private function buildInterviewSchedule($interview, $settings, $interviewPositions)
    {
        $schedule = [];
        $currentDate = new \DateTime($settings['interview_start_date']);
        $interviewDuration = (int)($settings['interview_duration'] ?? 60);
        $transitionPeriod = (int)($settings['transition_period'] ?? 10);
        $startTime = $settings['start_time'] ?? '08:00';
        $endTime = $settings['end_time'] ?? '15:00';
        $exclusionDates = $settings['exclusion_dates'] ?? [];
        $breakTimes = $settings['break_times'] ?? [];
        $specialTiming = $settings['special_timing'] ?? [];

        $slotDuration = $interviewDuration + $transitionPeriod; // Total time per candidate
        $candidateCounter = 1;

        foreach ($interviewPositions as $position) {
            // Get actual shortlisted applicants for this position
            $applicants = $this->getShortlistedApplicantsForPosition(
                $interview['org_id'],
                $interview['exercise_id'],
                $position['position_id']
            );

            foreach ($applicants as $applicant) {
                // Find next available slot
                $slot = $this->findNextAvailableSlot(
                    $currentDate,
                    $startTime,
                    $endTime,
                    $slotDuration,
                    $exclusionDates,
                    $breakTimes,
                    $specialTiming,
                    $schedule
                );

                if ($slot) {
                    $schedule[] = [
                        'candidate_number' => $candidateCounter++,
                        'applicant_name' => trim($applicant['first_name'] . ' ' . $applicant['last_name']),
                        'applicant_id' => $applicant['applicant_id'],
                        'application_number' => $applicant['application_number'],
                        'position' => $position,
                        'position_reference' => $position['position_reference'],
                        'position_title' => $position['designation'],
                        'date' => $slot['date']->format('Y-m-d'),
                        'start_time' => $slot['start_time'],
                        'end_time' => $slot['end_time'],
                        'duration' => $interviewDuration,
                        'priority' => $position['priority'] ?? null
                    ];

                    $currentDate = $slot['next_date'];
                }
            }
        }

        return $schedule;
    }

    /**
     * Find the next available time slot for an interview
     */
    private function findNextAvailableSlot($currentDate, $startTime, $endTime, $slotDuration, $exclusionDates, $breakTimes, $specialTiming, $existingSchedule)
    {
        $maxDays = 365; // Prevent infinite loop
        $dayCount = 0;

        while ($dayCount < $maxDays) {
            $dateStr = $currentDate->format('Y-m-d');

            // Skip weekends (optional - you can remove this if interviews are on weekends)
            if ($currentDate->format('N') >= 6) {
                $currentDate->add(new \DateInterval('P1D'));
                $dayCount++;
                continue;
            }

            // Skip exclusion dates
            if (in_array($dateStr, $exclusionDates)) {
                $currentDate->add(new \DateInterval('P1D'));
                $dayCount++;
                continue;
            }

            // Get working hours for this date
            $dayStartTime = $startTime;
            $dayEndTime = $endTime;

            // Check for special timing
            foreach ($specialTiming as $special) {
                if ($special['date'] === $dateStr) {
                    $dayStartTime = $special['start_time'];
                    $dayEndTime = $special['end_time'];
                    break;
                }
            }

            // Find available slot in this day
            $slot = $this->findSlotInDay($currentDate, $dayStartTime, $dayEndTime, $slotDuration, $breakTimes, $existingSchedule);

            if ($slot) {
                return $slot;
            }

            // Move to next day
            $currentDate->add(new \DateInterval('P1D'));
            $dayCount++;
        }

        return null; // No slot found
    }

    /**
     * Find an available slot within a specific day
     */
    private function findSlotInDay($date, $startTime, $endTime, $slotDuration, $breakTimes, $existingSchedule)
    {
        $currentTime = new \DateTime($date->format('Y-m-d') . ' ' . $startTime);
        $endDateTime = new \DateTime($date->format('Y-m-d') . ' ' . $endTime);

        while ($currentTime < $endDateTime) {
            $slotEndTime = clone $currentTime;
            $slotEndTime->add(new \DateInterval('PT' . $slotDuration . 'M'));

            // Check if slot fits within working hours
            if ($slotEndTime > $endDateTime) {
                break;
            }

            // Check for conflicts with break times
            $hasBreakConflict = false;
            foreach ($breakTimes as $breakTime) {
                $breakStart = new \DateTime($date->format('Y-m-d') . ' ' . $breakTime['start_time']);
                $breakEnd = new \DateTime($date->format('Y-m-d') . ' ' . $breakTime['end_time']);

                if ($currentTime < $breakEnd && $slotEndTime > $breakStart) {
                    $hasBreakConflict = true;
                    $currentTime = $breakEnd; // Move to after break
                    break;
                }
            }

            if ($hasBreakConflict) {
                continue;
            }

            // Check for conflicts with existing schedule
            $hasScheduleConflict = false;
            foreach ($existingSchedule as $existing) {
                if ($existing['date'] === $date->format('Y-m-d')) {
                    $existingStart = new \DateTime($existing['date'] . ' ' . $existing['start_time']);
                    $existingEnd = new \DateTime($existing['date'] . ' ' . $existing['end_time']);

                    if ($currentTime < $existingEnd && $slotEndTime > $existingStart) {
                        $hasScheduleConflict = true;
                        break;
                    }
                }
            }

            if (!$hasScheduleConflict) {
                return [
                    'date' => clone $date,
                    'start_time' => $currentTime->format('H:i'),
                    'end_time' => $slotEndTime->format('H:i'),
                    'next_date' => clone $date
                ];
            }

            // Move to next possible slot
            $currentTime->add(new \DateInterval('PT' . $slotDuration . 'M'));
        }

        return null;
    }

    /**
     * Generate Interview Schedule PDF
     */
    private function generateInterviewSchedulePDF($exercise, $interview, $settings, $schedule)
    {
        try {
            // Create TCPDF with custom footer
            $pdf = new class('L', 'mm', 'A4', true, 'UTF-8', false) extends \TCPDF {
                public function Footer() {
                    $this->SetY(-18);
                    $footerY = $this->GetY();
                    $footerHeight = 15;
                    $footerWidth = $this->getPageWidth() - 20; // Account for margins

                    // Draw PNG flag colored sections
                    $this->SetFillColor(240, 15, 0); // Red
                    $this->Rect(10, $footerY, $footerWidth / 3, $footerHeight, 'F');

                    $this->SetFillColor(0, 0, 0); // Black
                    $this->Rect(10 + ($footerWidth / 3), $footerY, $footerWidth / 3, $footerHeight, 'F');

                    $this->SetFillColor(255, 194, 15); // Gold
                    $this->Rect(10 + (2 * $footerWidth / 3), $footerY, $footerWidth / 3, $footerHeight, 'F');

                    // Add content
                    $this->SetFont('helvetica', '', 8);
                    $this->SetY($footerY + 1);

                    // Row 1
                    $this->SetTextColor(255, 255, 255);
                    $this->SetX(12);
                    $this->Cell(90, 3, 'Generated by DERS v1.0', 0, 0, 'L');

                    $this->SetX(100);
                    $this->Cell(87, 3, 'Dakoii Echad Recruitment & Selection System', 0, 0, 'C');

                    $this->SetTextColor(0, 0, 0);
                    $this->SetX(185);
                    $this->Cell(90, 3, 'Generated on: ' . date('M d, Y H:i') . ' | Page ' . $this->getAliasNumPage() . ' of ' . $this->getAliasNbPages(), 0, 0, 'R');

                    // Row 2
                    $this->SetY($this->GetY() + 3);
                    $this->SetTextColor(255, 255, 255);
                    $this->SetX(12);
                    $this->Cell(90, 3, 'AI-Powered Interview Scheduling', 0, 0, 'L');

                    $this->SetX(100);
                    $this->Cell(87, 3, 'Developed by Dakoii Systems & Echad Consultancy Services', 0, 0, 'C');

                    // Row 3
                    $this->SetY($this->GetY() + 3);
                    $this->SetX(10);
                    $this->Cell($footerWidth, 3, 'ders.dakoiims.com', 0, 0, 'C');

                    $this->SetTextColor(0, 0, 0);
                }
            };

            // Configure PDF
            $pdf->SetCreator('DERS System');
            $pdf->SetTitle('Interview Schedule - ' . $interview['interview_title']);
            $pdf->SetMargins(10, 20, 10);
            $pdf->SetAutoPageBreak(true, 25);
            $pdf->setPrintHeader(false);
            $pdf->setPrintFooter(true);

            // Add page and content
            $pdf->AddPage();

            // Add logo
            $logoPath = FCPATH . 'public/assets/system_img/system-logo.png';
            if (file_exists($logoPath)) {
                $pdf->Image($logoPath, ($pdf->getPageWidth() - 20) / 2, $pdf->GetY(), 20, 20);
                $pdf->Ln(25);
            }

            // Add title
            $pdf->SetFont('helvetica', 'B', 16);
            $pdf->Cell(0, 10, 'Interview Schedule', 0, 1, 'C');
            $pdf->Ln(5);

            // Add interview details
            $this->addInterviewScheduleContent($pdf, $exercise, $interview, $settings, $schedule);

            // Generate filename and output directly to browser
            $filename = 'interview_schedule_' . strtolower(str_replace(' ', '_', $interview['interview_title'])) . '_' . date('Y-m-d_H-i-s') . '.pdf';
            $pdf->Output($filename, 'D'); // 'D' = Direct download to browser

            // No return needed - PDF is sent directly to browser

        } catch (\Exception $e) {
            log_message('error', 'Interview Schedule PDF Generation Error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Add Interview Schedule Content to PDF
     */
    private function addInterviewScheduleContent($pdf, $exercise, $interview, $settings, $schedule)
    {
        // Interview Information Section (matching local version exactly)
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->Cell(0, 8, 'Interview Information', 0, 1, 'L');
        $pdf->Ln(2);

        $pdf->SetFont('helvetica', '', 10);

        // Single column layout for interview details
        $leftX = 10;

        // Basic interview information
        $pdf->SetX($leftX);
        $pdf->Cell(60, 6, 'Interview Title:', 0, 0, 'L');
        $pdf->Cell(80, 6, $interview['interview_title'], 0, 1, 'L');

        $pdf->SetX($leftX);
        $pdf->Cell(60, 6, 'Exercise:', 0, 0, 'L');
        $pdf->Cell(80, 6, $interview['exercise_name'], 0, 1, 'L');

        $pdf->SetX($leftX);
        $pdf->Cell(60, 6, 'Organization:', 0, 0, 'L');
        $pdf->Cell(80, 6, $interview['org_name'], 0, 1, 'L');

        $pdf->SetX($leftX);
        $pdf->Cell(60, 6, 'Venue:', 0, 0, 'L');
        $pdf->Cell(80, 6, $settings['venue'] ?? 'Not specified', 0, 1, 'L');

        // Add spacing before additional details
        $pdf->Ln(3);

        // Additional interview details below organization
        $pdf->SetX($leftX);
        $pdf->Cell(60, 6, 'Interview Duration:', 0, 0, 'L');
        $pdf->Cell(60, 6, ($settings['interview_duration'] ?? 60) . ' minutes', 0, 1, 'L');

        $pdf->SetX($leftX);
        $pdf->Cell(60, 6, 'Working Hours:', 0, 0, 'L');
        $pdf->Cell(60, 6, ($settings['start_time'] ?? '08:00') . ' - ' . ($settings['end_time'] ?? '15:00'), 0, 1, 'L');

        $pdf->SetX($leftX);
        $pdf->Cell(60, 6, 'Total Interviews:', 0, 0, 'L');
        $pdf->Cell(60, 6, count($schedule) . ' interviews', 0, 1, 'L');

        $pdf->Ln(10);

        // Schedule Table
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->Cell(0, 8, 'Interview Schedule', 0, 1, 'L');
        $pdf->Ln(2);

        // Table headers
        $pdf->SetFont('helvetica', 'B', 8);
        $pdf->SetFillColor(220, 220, 220);

        // Column widths (total: 277mm for landscape A4 with 10mm margins)
        $colWidths = [
            'num' => 15,
            'name' => 50,
            'ref' => 25,
            'position' => 45,
            'date' => 30,
            'time' => 40,
            'duration' => 20,
            'priority' => 22
        ];

        // Header row
        $pdf->Cell($colWidths['num'], 8, '#', 1, 0, 'C', true);
        $pdf->Cell($colWidths['name'], 8, 'Applicant Name', 1, 0, 'C', true);
        $pdf->Cell($colWidths['ref'], 8, 'Position Ref', 1, 0, 'C', true);
        $pdf->Cell($colWidths['position'], 8, 'Position', 1, 0, 'C', true);
        $pdf->Cell($colWidths['date'], 8, 'Date', 1, 0, 'C', true);
        $pdf->Cell($colWidths['time'], 8, 'Time From - Time To', 1, 0, 'C', true);
        $pdf->Cell($colWidths['duration'], 8, 'Duration', 1, 0, 'C', true);
        $pdf->Cell($colWidths['priority'], 8, 'Priority', 1, 1, 'C', true);

        // Data rows
        $pdf->SetFont('helvetica', '', 8);
        $pdf->SetFillColor(255, 255, 255);

        foreach ($schedule as $slot) {
            // Check if we need a new page
            if ($pdf->GetY() > 180) { // Leave space for footer
                $pdf->AddPage();

                // Repeat headers on new page
                $pdf->SetFont('helvetica', 'B', 8);
                $pdf->SetFillColor(220, 220, 220);

                $pdf->Cell($colWidths['num'], 8, '#', 1, 0, 'C', true);
                $pdf->Cell($colWidths['name'], 8, 'Applicant Name', 1, 0, 'C', true);
                $pdf->Cell($colWidths['ref'], 8, 'Position Ref', 1, 0, 'C', true);
                $pdf->Cell($colWidths['position'], 8, 'Position', 1, 0, 'C', true);
                $pdf->Cell($colWidths['date'], 8, 'Date', 1, 0, 'C', true);
                $pdf->Cell($colWidths['time'], 8, 'Time From - Time To', 1, 0, 'C', true);
                $pdf->Cell($colWidths['duration'], 8, 'Duration', 1, 0, 'C', true);
                $pdf->Cell($colWidths['priority'], 8, 'Priority', 1, 1, 'C', true);

                $pdf->SetFont('helvetica', '', 8);
                $pdf->SetFillColor(255, 255, 255);
            }

            $rowHeight = 8;

            $pdf->Cell($colWidths['num'], $rowHeight, $slot['candidate_number'], 1, 0, 'C');
            $pdf->Cell($colWidths['name'], $rowHeight, $slot['applicant_name'], 1, 0, 'L');
            $pdf->Cell($colWidths['ref'], $rowHeight, $slot['position_reference'], 1, 0, 'C');
            $pdf->Cell($colWidths['position'], $rowHeight, $slot['position_title'], 1, 0, 'L');
            $pdf->Cell($colWidths['date'], $rowHeight, date('M d, Y', strtotime($slot['date'])), 1, 0, 'C');
            $pdf->Cell($colWidths['time'], $rowHeight,
                date('h:i A', strtotime($slot['start_time'])) . ' - ' . date('h:i A', strtotime($slot['end_time'])),
                1, 0, 'C');
            $pdf->Cell($colWidths['duration'], $rowHeight, $slot['duration'] . ' min', 1, 0, 'C');
            $pdf->Cell($colWidths['priority'], $rowHeight, $slot['priority'] ?: 'N/A', 1, 1, 'C');
        }

        // Summary section
        $pdf->Ln(10);
        $pdf->SetFont('helvetica', 'B', 10);
        $pdf->Cell(0, 6, 'Schedule Summary:', 0, 1, 'L');

        $pdf->SetFont('helvetica', '', 9);
        $pdf->Cell(0, 5, '• Total Interviews: ' . count($schedule), 0, 1, 'L');

        if (!empty($schedule)) {
            $startDate = date('M d, Y', strtotime($schedule[0]['date']));
            $endDate = date('M d, Y', strtotime(end($schedule)['date']));
            $pdf->Cell(0, 5, '• Interview Period: ' . $startDate . ' to ' . $endDate, 0, 1, 'L');
        }

        $pdf->Cell(0, 5, '• Interview Duration: ' . ($settings['interview_duration'] ?? 60) . ' minutes per interview', 0, 1, 'L');
        $pdf->Cell(0, 5, '• Transition Period: ' . ($settings['transition_period'] ?? 10) . ' minutes between interviews', 0, 1, 'L');

        if (!empty($settings['venue'])) {
            $pdf->Cell(0, 5, '• Venue: ' . $settings['venue'], 0, 1, 'L');
        }
    }
}
