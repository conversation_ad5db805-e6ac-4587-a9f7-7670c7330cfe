# GCS File Path Updates Guide

## Overview
This guide documents the comprehensive updates made to prioritize `public_url` over `file_path` for file downloads and viewing across the DERS application. These changes ensure consistent file access for both Google Cloud Storage (GCS) and local files.

## Problem Statement
The application was inconsistently using `file_path` for file access, which caused issues when files were stored in Google Cloud Storage. Files stored in GCS should use the `public_url` field for direct access, while local files can still use `file_path` as a fallback.

## Solution Overview
Updated the `AppxApplicationFilesModel` and all related views and controllers to:
1. Always prioritize `public_url` first
2. Fallback to `file_path` for local files
3. Provide proper error handling when files are not available

---

## 1. Model Updates

### AppxApplicationFilesModel.php

#### Updated `getFileUrl()` Method
```php
/**
 * Get file URL - prioritizes public_url across all instances
 *
 * @param array $file
 * @return string
 */
public function getFileUrl(array $file): string
{
    // Always prioritize public_url if available
    if (!empty($file['public_url'])) {
        return $file['public_url'];
    }

    // Fallback to local file path
    if (!empty($file['file_path'])) {
        return base_url($file['file_path']);
    }

    return '#';
}
```

#### Added New Helper Method
```php
/**
 * Get files by applicant ID with enhanced data including URLs
 *
 * @param int $applicantId
 * @return array
 */
public function getFilesByApplicantIdWithUrls(int $applicantId): array
{
    $files = $this->getFilesByApplicantId($applicantId);

    foreach ($files as &$file) {
        $file['view_url'] = $this->getFileUrl($file);
        $file['storage_display'] = (isset($file['storage_type']) && $file['storage_type'] === 'gcs') ? 'Cloud Storage' : 'Local Storage';
    }

    return $files;
}
```

---

## 2. Controller Updates

### IncomingApplicantsController.php
**File:** `app/Controllers/IncomingApplicantsController.php`

**Changes:**
- Line 325: Updated to use `getFilesByApplicantId()` instead of direct query
- Lines 327-336: Updated file path assignment to prioritize `public_url`

```php
// Get application files using applicant_id
$appxFilesModel = new \App\Models\AppxApplicationFilesModel();
$applicationFiles = $appxFilesModel->getFilesByApplicantId($application['applicant_id']);

// Set file paths for compatibility and organize files - prioritize public_url
foreach ($applicationFiles as $file) {
    $fileUrl = !empty($file['public_url']) ? $file['public_url'] : (!empty($file['file_path']) ? base_url($file['file_path']) : '');
    
    if (strpos($file['file_title'], 'CV') !== false || strpos($file['file_title'], 'Resume') !== false) {
        $application['cv_path'] = $fileUrl;
    } elseif (strpos($file['file_title'], 'Cover') !== false) {
        $application['cover_letter_path'] = $fileUrl;
    }
}
```

### ApplicantApplicationController.php
**File:** `app/Controllers/ApplicantApplicationController.php`

**Changes:**
- Line 160: Updated to use `getFilesByApplicantId()`
- Line 302: Updated to use `getFilesByApplicantId()`
- Lines 446-450: Removed `application_id` from file verification query

### AcknowledgedApplicationsController.php
**File:** `app/Controllers/AcknowledgedApplicationsController.php`

**Changes:**
- Line 116: Updated to use `getFilesByApplicantId()`

---

## 3. View Updates

### Applicant Application View
**File:** `app/Views/applicant/applicant_application_view.php`

**Updated Supporting Documents Section (Lines 513-526):**
```php
<td>
    <?php 
    $fileUrl = !empty($file['public_url']) ? $file['public_url'] : (!empty($file['file_path']) ? base_url($file['file_path']) : '');
    ?>
    <?php if (!empty($fileUrl)): ?>
        <a href="<?= $fileUrl ?>"
           target="_blank"
           class="btn btn-sm btn-outline-primary">
            <i class="fas fa-eye me-1"></i>View
        </a>
    <?php else: ?>
        <span class="text-muted">File not available</span>
    <?php endif; ?>
</td>
```

### Application Pre-screening Detailed View
**File:** `app/Views/application_pre_screening/application_pre_screening_detailed_view.php`

**Updated File Actions (Lines 397-413):**
```php
<div>
    <?php 
    $fileUrl = !empty($file['public_url']) ? $file['public_url'] : (!empty($file['file_path']) ? base_url($file['file_path']) : '#');
    ?>
    <?php if ($fileUrl !== '#'): ?>
        <a href="<?= $fileUrl ?>" class="btn btn-sm btn-primary me-1" target="_blank">
            <i class="fas fa-eye me-1"></i> View
        </a>
        <a href="<?= $fileUrl ?>" class="btn btn-sm btn-success" download>
            <i class="fas fa-download me-1"></i> Download
        </a>
    <?php else: ?>
        <span class="btn btn-sm btn-secondary disabled">
            <i class="fas fa-exclamation-triangle me-1"></i> File not available
        </span>
    <?php endif; ?>
</div>
```

### Applicant Application Files Edit View
**File:** `app/Views/applicant/applicant_application_files_edit.php`

**Updated File View Buttons (Lines 126-138):**
```php
<div class="btn-group-vertical ms-3">
    <?php 
    $fileUrl = !empty($file['public_url']) ? $file['public_url'] : (!empty($file['file_path']) ? base_url($file['file_path']) : '');
    ?>
    <?php if (!empty($fileUrl)): ?>
        <a href="<?= $fileUrl ?>"
           target="_blank"
           class="btn btn-sm btn-outline-primary mb-1">
            <i class="fas fa-eye me-1"></i>View
        </a>
    <?php else: ?>
        <span class="btn btn-sm btn-outline-secondary mb-1 disabled">
            <i class="fas fa-exclamation-triangle me-1"></i>File not available
```

### Applicant Files Edit View
**File:** `app/Views/applicant/applicant_files_edit.php`

**Updated PDF View Link (Lines 69-82):**
```php
<div class="col-md-6">
    <?php 
    $fileUrl = !empty($file['public_url']) ? $file['public_url'] : (!empty($file['file_path']) ? base_url($file['file_path']) : '');
    ?>
    <?php if (!empty($fileUrl)): ?>
        <a href="<?= $fileUrl ?>" class="btn btn-sm btn-outline-primary" target="_blank">
            <i class="fas fa-file-pdf me-1"></i>View PDF
        </a>
    <?php else: ?>
        <span class="btn btn-sm btn-outline-secondary disabled">
            <i class="fas fa-exclamation-triangle me-1"></i>File not available
        </span>
    <?php endif; ?>
</div>
```

### Acknowledged Applications View
**File:** `app/Views/acknowledged_applications/acknowledged_applications_view.php`

**Updated File Download Links (Lines 176-185):**
```php
<?php 
$fileUrl = !empty($file['public_url']) ? $file['public_url'] : (!empty($file['file_path']) ? base_url($file['file_path']) : '');
?>
<?php if (!empty($fileUrl)): ?>
    <a href="<?= $fileUrl ?>" target="_blank" class="btn btn-sm btn-outline-primary">
        <i class="fas fa-download"></i> View
    </a>
<?php else: ?>
    <span class="text-muted">File not available</span>
<?php endif; ?>
```

### Incoming Applicants View
**File:** `app/Views/incoming_applicants/incoming_applicants_view.php`

**Updated File Download Buttons (Lines 205-216):**
```php
<?php 
$fileUrl = !empty($file['public_url']) ? $file['public_url'] : (!empty($file['file_path']) ? base_url($file['file_path']) : '');
?>
<?php if (!empty($fileUrl)): ?>
    <a href="<?= $fileUrl ?>" download class="btn btn-sm btn-outline-secondary flex-fill">
        <i class="fas fa-download me-1"></i>Download
    </a>
<?php else: ?>
    <span class="btn btn-sm btn-outline-secondary flex-fill disabled">
        <i class="fas fa-exclamation-triangle me-1"></i>File not available
    </span>
<?php endif; ?>
```

---

## 4. Key Benefits

### Consistent File Access
- All file access now prioritizes `public_url` across the entire application
- Unified approach to file URL generation

### Cloud Storage Support
- Properly handles files stored in Google Cloud Storage
- Direct access to GCS files without server-side processing

### Fallback Mechanism
- Still works with local files if `public_url` is not available
- Backward compatibility maintained

### Better Error Handling
- Shows "File not available" when neither URL is accessible
- Prevents broken links and 404 errors

### Performance Improvements
- Direct access to cloud files reduces server load
- No need to proxy file requests through the application

---

## 5. Testing Checklist

After implementing these changes, verify the following:

- [ ] Application details page shows file download links correctly
- [ ] Files stored in GCS open directly via `public_url`
- [ ] Local files still work via `file_path` fallback
- [ ] "File not available" message appears for missing files
- [ ] Pre-screening views show file links correctly
- [ ] Acknowledged applications show file links correctly
- [ ] File edit interfaces work properly

---

## 6. Future Considerations

### Migration Strategy
Consider migrating all existing `file_path` references to use the model's `getFileUrl()` method for consistency.

### Caching
Implement URL caching for frequently accessed files to improve performance.

### Security
Ensure proper access controls are maintained when using direct GCS URLs.

---

## Conclusion

These updates provide a robust, scalable solution for file access that works seamlessly with both local storage and Google Cloud Storage. The changes maintain backward compatibility while providing a clear path forward for cloud-first file storage.
