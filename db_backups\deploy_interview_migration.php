<?php
/**
 * Production Migration Deployment Script
 * Interview Notification System Database Migration
 * 
 * This script deploys the interview notification fields to the production database
 * using the CodeIgniter 4 migration system.
 * 
 * Prerequisites:
 * 1. SSH tunnel must be established: ssh -L 3307:127.0.0.1:3306 <EMAIL> -N
 * 2. Database backup should be created before running this script
 * 3. Production database credentials must be configured
 * 
 * Usage: php deploy_interview_migration.php
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include CodeIgniter bootstrap
require_once __DIR__ . '/../vendor/autoload.php';

// Load CodeIgniter
$app = \Config\Services::codeigniter();
$app->initialize();

echo "=== Interview Notification System - Production Migration Deployment ===\n";
echo "Date: " . date('Y-m-d H:i:s') . "\n";
echo "Target Database: dakoiim1_ders_db (Production)\n";
echo "Migration: AddInterviewNotificationFields\n\n";

try {
    // Configure production database connection
    $config = new \Config\Database();
    
    // Override with production settings
    $config->default = [
        'DSN'      => '',
        'hostname' => '127.0.0.1',
        'username' => 'dakoiim1_ders_db_admin',
        'password' => 'dakoiianzii',
        'database' => 'dakoiim1_ders_db',
        'DBDriver' => 'MySQLi',
        'DBPrefix' => '',
        'pConnect' => false,
        'DBDebug'  => true,
        'charset'  => 'utf8',
        'DBCollat' => 'utf8_general_ci',
        'swapPre'  => '',
        'encrypt'  => false,
        'compress' => false,
        'strictOn' => false,
        'failover' => [],
        'port'     => 3307, // SSH tunnel port
    ];

    echo "1. Connecting to production database...\n";
    
    // Test database connection
    $db = \Config\Database::connect();
    
    if (!$db->connID) {
        throw new \Exception("Failed to connect to production database");
    }
    
    echo "   ✅ Successfully connected to production database\n\n";

    // Check if SSH tunnel is active
    echo "2. Verifying SSH tunnel connection...\n";
    $result = $db->query("SELECT 1 as test");
    if (!$result) {
        throw new \Exception("SSH tunnel may not be active. Please ensure: ssh -L 3307:127.0.0.1:3306 <EMAIL> -N");
    }
    echo "   ✅ SSH tunnel is active and working\n\n";

    // Verify target table exists
    echo "3. Verifying target table exists...\n";
    if (!$db->tableExists('appx_application_details')) {
        throw new \Exception("Target table 'appx_application_details' does not exist");
    }
    echo "   ✅ Target table 'appx_application_details' exists\n\n";

    // Check current table structure
    echo "4. Checking current table structure...\n";
    $fields = $db->getFieldNames('appx_application_details');
    $interviewFields = array_filter($fields, function($field) {
        return strpos($field, 'interview_notified') !== false;
    });
    
    if (!empty($interviewFields)) {
        echo "   ⚠️  Some interview notification fields already exist:\n";
        foreach ($interviewFields as $field) {
            echo "      - {$field}\n";
        }
        echo "   Migration will skip existing fields.\n\n";
    } else {
        echo "   ✅ No interview notification fields found. Ready for migration.\n\n";
    }

    // Create backup
    echo "5. Creating database backup...\n";
    $backupFile = __DIR__ . '/backup_before_migration_' . date('Y-m-d_H-i-s') . '.sql';
    $backupCommand = "mysqldump -h 127.0.0.1 -P 3307 -u dakoiim1_ders_db_admin -pdakoiianzii dakoiim1_ders_db appx_application_details > {$backupFile}";
    
    echo "   Executing backup command...\n";
    $output = [];
    $returnCode = 0;
    exec($backupCommand, $output, $returnCode);
    
    if ($returnCode === 0 && file_exists($backupFile)) {
        echo "   ✅ Backup created successfully: {$backupFile}\n";
        echo "   Backup size: " . number_format(filesize($backupFile)) . " bytes\n\n";
    } else {
        echo "   ⚠️  Backup creation failed or file not found. Proceeding with caution...\n\n";
    }

    // Run the migration
    echo "6. Executing migration...\n";
    
    // Load migration runner
    $migrate = \Config\Services::migrations();
    
    // Set database group to use our production config
    $migrate->setNamespace(null);
    
    echo "   Running AddInterviewNotificationFields migration...\n";
    
    // Execute the specific migration
    $migrationFile = '2025-01-29-120000_AddInterviewNotificationFields';
    
    // Load the migration class manually for better control
    require_once APPPATH . 'Database/Migrations/2025-01-29-120000_AddInterviewNotificationFields.php';
    
    $migration = new \App\Database\Migrations\AddInterviewNotificationFields();
    $migration->up();
    
    echo "   ✅ Migration executed successfully\n\n";

    // Verify migration results
    echo "7. Verifying migration results...\n";
    
    $newFields = $db->getFieldNames('appx_application_details');
    $addedFields = array_filter($newFields, function($field) {
        return strpos($field, 'interview_notified') !== false;
    });
    
    echo "   Interview notification fields found:\n";
    foreach ($addedFields as $field) {
        echo "      ✅ {$field}\n";
    }
    
    // Check field details
    echo "\n   Field details:\n";
    $fieldData = $db->getFieldData('appx_application_details');
    foreach ($fieldData as $field) {
        if (strpos($field->name, 'interview_notified') !== false) {
            echo "      - {$field->name}: {$field->type}";
            if ($field->max_length) echo "({$field->max_length})";
            echo $field->nullable ? " NULL" : " NOT NULL";
            if ($field->default !== null) echo " DEFAULT '{$field->default}'";
            echo "\n";
        }
    }

    echo "\n8. Testing field functionality...\n";
    
    // Test that we can query the new fields
    $testQuery = "SELECT id, interview_notified_at, interview_notified_by, interview_notified_count, interview_notified_email, interview_notified_remarks FROM appx_application_details LIMIT 1";
    $testResult = $db->query($testQuery);
    
    if ($testResult) {
        echo "   ✅ New fields are queryable and functional\n";
    } else {
        throw new \Exception("Failed to query new fields");
    }

    echo "\n=== MIGRATION DEPLOYMENT COMPLETED SUCCESSFULLY ===\n";
    echo "✅ All interview notification fields have been added to production database\n";
    echo "✅ Database backup created: {$backupFile}\n";
    echo "✅ Migration verified and tested\n";
    echo "✅ Interview Notification System is ready for production use\n\n";
    
    echo "Next Steps:\n";
    echo "1. Deploy the Interview Notification System code to production\n";
    echo "2. Test the notification functionality on production\n";
    echo "3. Monitor application logs for any issues\n";
    echo "4. Keep the backup file for rollback if needed\n";

} catch (\Exception $e) {
    echo "\n❌ MIGRATION DEPLOYMENT FAILED\n";
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n\n";
    
    echo "Rollback Instructions:\n";
    echo "1. If fields were partially added, run the migration rollback:\n";
    echo "   php spark migrate:rollback\n";
    echo "2. Or manually remove fields:\n";
    echo "   ALTER TABLE appx_application_details DROP COLUMN IF EXISTS interview_notified_at;\n";
    echo "   ALTER TABLE appx_application_details DROP COLUMN IF EXISTS interview_notified_by;\n";
    echo "   ALTER TABLE appx_application_details DROP COLUMN IF EXISTS interview_notified_count;\n";
    echo "   ALTER TABLE appx_application_details DROP COLUMN IF EXISTS interview_notified_email;\n";
    echo "   ALTER TABLE appx_application_details DROP COLUMN IF EXISTS interview_notified_remarks;\n";
    echo "3. Restore from backup if available\n";
    
    exit(1);
}
?>
