-- Database Backup Script for Interview Notification Migration
-- Created: 2025-01-29
-- Purpose: Backup appx_application_details table before adding interview notification fields
-- 
-- This script should be executed BEFORE running the interview notification migration
-- to ensure we can rollback if needed.
--
-- Usage:
-- 1. Connect to production database using SSH tunnel
-- 2. Execute: mysqldump -h 127.0.0.1 -P 3307 -u dakoiim1_ders_db_admin -p dakoiim1_ders_db appx_application_details > backup_before_interview_migration.sql
-- 3. Verify backup file is created successfully
-- 4. Then proceed with migration

-- Backup command for reference:
-- mysqldump -h 127.0.0.1 -P 3307 -u dakoiim1_ders_db_admin -p dakoiim1_ders_db appx_application_details > backup_before_interview_migration_$(date +%Y%m%d_%H%M%S).sql

-- Verification queries to run after backup:
-- SELECT COUNT(*) FROM appx_application_details;
-- DESCRIBE appx_application_details;
-- SELECT * FROM appx_application_details LIMIT 5;

-- Migration rollback plan if needed:
-- 1. Drop the new fields if they were added:
--    ALTER TABLE appx_application_details DROP COLUMN IF EXISTS interview_notified_at;
--    ALTER TABLE appx_application_details DROP COLUMN IF EXISTS interview_notified_by;
--    ALTER TABLE appx_application_details DROP COLUMN IF EXISTS interview_notified_count;
--    ALTER TABLE appx_application_details DROP COLUMN IF EXISTS interview_notified_email;
--    ALTER TABLE appx_application_details DROP COLUMN IF EXISTS interview_notified_remarks;
--
-- 2. Or restore from backup:
--    mysql -h 127.0.0.1 -P 3307 -u dakoiim1_ders_db_admin -p dakoiim1_ders_db < backup_before_interview_migration.sql

-- Pre-migration verification queries:
-- Check current table structure
DESCRIBE appx_application_details;

-- Check if any interview notification fields already exist
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'dakoiim1_ders_db' 
  AND TABLE_NAME = 'appx_application_details' 
  AND COLUMN_NAME LIKE '%interview_notified%';

-- Count total records for verification
SELECT COUNT(*) as total_records FROM appx_application_details;

-- Sample data check
SELECT id, application_number, first_name, last_name, created_at 
FROM appx_application_details 
ORDER BY created_at DESC 
LIMIT 5;
