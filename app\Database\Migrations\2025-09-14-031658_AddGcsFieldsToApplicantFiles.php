<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddGcsFieldsToApplicantFiles extends Migration
{
    public function up()
    {
        // Add GCS fields to applicant_files table
        if ($this->db->tableExists('applicant_files')) {
            $fields = [
                'gcs_path' => [
                    'type' => 'VARCHAR',
                    'constraint' => 500,
                    'null' => true,
                    'comment' => 'Google Cloud Storage path'
                ],
                'storage_type' => [
                    'type' => 'ENUM',
                    'constraint' => ['local', 'gcs'],
                    'default' => 'local',
                    'comment' => 'Storage type'
                ],
                'public_url' => [
                    'type' => 'VARCHAR',
                    'constraint' => 500,
                    'null' => true,
                    'comment' => 'Public URL for file access'
                ]
            ];
            $this->forge->addColumn('applicant_files', $fields);
        }
    }

    public function down()
    {
        // Remove GCS fields from applicant_files table
        if ($this->db->tableExists('applicant_files')) {
            $this->forge->dropColumn('applicant_files', [
                'gcs_path',
                'storage_type',
                'public_url'
            ]);
        }
    }
}
