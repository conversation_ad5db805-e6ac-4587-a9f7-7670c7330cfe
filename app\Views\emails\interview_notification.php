<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interview Notification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(to bottom left, #F00F00 0%, #D00D00 40%, #000000 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
            position: relative;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }
        .content {
            padding: 30px 20px;
        }
        .highlight-text {
            color: #F00F00;
            font-weight: bold;
        }
        .interview-details {
            border-left: 4px solid #F00F00;
            padding-left: 15px;
            margin: 20px 0;
            background: linear-gradient(to right, rgba(240, 15, 0, 0.05) 0%, rgba(0, 0, 0, 0.02) 100%);
            padding: 20px;
            border-radius: 5px;
        }
        .interview-details h3 {
            color: #F00F00;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: bold;
        }
        .detail-row {
            margin-bottom: 10px;
        }
        .detail-label {
            font-weight: bold;
            color: #555;
            display: inline-block;
            min-width: 140px;
        }
        .detail-value {
            color: #333;
        }
        .schedule-highlight {
            border-left: 4px solid #F00F00;
            padding-left: 15px;
            margin: 20px 0;
            background: linear-gradient(to right, rgba(240, 15, 0, 0.05) 0%, rgba(0, 0, 0, 0.02) 100%);
            padding: 20px;
            border-radius: 5px;
        }
        .schedule-highlight h4 {
            color: #F00F00;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 16px;
            font-weight: bold;
        }
        .special-instructions {
            border-left: 4px solid #F00F00;
            padding-left: 15px;
            margin: 20px 0;
            background: linear-gradient(to right, rgba(240, 15, 0, 0.05) 0%, rgba(0, 0, 0, 0.02) 100%);
            padding: 20px;
            border-radius: 5px;
        }
        .special-instructions h4 {
            color: #F00F00;
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 16px;
            font-weight: bold;
        }
        .important-notes {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .important-notes h4 {
            margin-top: 0;
            color: #333;
            font-size: 16px;
            font-weight: bold;
        }
        .footer {
            background: linear-gradient(to bottom left, rgba(240, 15, 0, 0.03) 0%, rgba(0, 0, 0, 0.08) 100%);
            padding: 20px;
            text-align: center;
            font-size: 12px;
            color: #666;
        }
        @media (max-width: 600px) {
            .detail-label {
                display: block;
                min-width: auto;
                margin-bottom: 5px;
            }
            .content {
                padding: 20px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <h1>Interview Notification</h1>
        </div>

        <!-- Content -->
        <div class="content">
            <p>Dear <span class="highlight-text"><?= esc($applicant['first_name'] . ' ' . $applicant['last_name']) ?></span>,</p>

            <p>We are pleased to inform you that you have been scheduled for an interview for the position you applied for with <span class="highlight-text"><?= esc($interview['org_name']) ?></span>. Please find the details below:</p>

            <!-- Application Details -->
            <div class="interview-details">
                <h3>Application Details</h3>

                <div class="detail-row">
                    <span class="detail-label">Position:</span>
                    <span class="detail-value"><?= esc($slot['position_title']) ?></span>
                </div>

                <div class="detail-row">
                    <span class="detail-label">Position Reference:</span>
                    <span class="detail-value"><?= esc($slot['position_reference']) ?></span>
                </div>

                <div class="detail-row">
                    <span class="detail-label">Application Number:</span>
                    <span class="detail-value"><?= esc($slot['application_number']) ?></span>
                </div>
            </div>

            <!-- Conditional Content Based on Notification Type -->
            <?php if (isset($notification_type) && $notification_type === 'scheduled'): ?>
                <!-- Interview Schedule (Only for scheduled notifications) -->
                <div class="schedule-highlight">
                    <h4>📅 Interview Schedule</h4>
                    <div class="detail-row">
                        <span class="detail-label">Date:</span>
                        <span class="detail-value"><?= date('l, F d, Y', strtotime($slot['date'])) ?></span>
                    </div>

                    <div class="detail-row">
                        <span class="detail-label">Time:</span>
                        <span class="detail-value"><?= date('h:i A', strtotime($slot['start_time'])) ?> - <?= date('h:i A', strtotime($slot['end_time'])) ?></span>
                    </div>

                    <div class="detail-row">
                        <span class="detail-label">Duration:</span>
                        <span class="detail-value"><?= esc($slot['duration']) ?> minutes</span>
                    </div>
                </div>
            <?php else: ?>
                <!-- General Interview Notification (Without specific schedule) -->
                <div class="schedule-highlight">
                    <h4>📅 Interview Invitation</h4>
                    <p>You have been selected for an interview for the above position. The specific interview schedule will be communicated to you separately.</p>
                    <p>Please ensure your contact details are up to date so we can reach you with the interview schedule.</p>
                </div>
            <?php endif; ?>

            <!-- Special Instructions -->
            <?php if (!empty($special_instructions)): ?>
            <div class="special-instructions">
                <h4>📋 Special Instructions</h4>
                <p><?= nl2br(esc($special_instructions)) ?></p>
            </div>
            <?php endif; ?>

            <!-- Important Notes -->
            <div class="important-notes">
                <h4>Important Notes:</h4>
                <ul>
                    <li>Please arrive at least 15 minutes before your scheduled interview time</li>
                    <li>Bring a valid photo ID and any required documents</li>
                    <li>If you need to reschedule or have any questions, please contact us immediately</li>
                    <li>Failure to attend the interview without prior notice may result in disqualification</li>
                </ul>
            </div>

            <p>We look forward to meeting with you and wish you the best of luck with your interview.</p>

            <p>Best regards,<br>
            <strong>The DERS Team</strong><br>
            <?= esc($interview['org_name']) ?></p>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>This is an automated message, please do not reply to this email.</p>
            <p>For inquiries, please contact the recruitment team through the official channels.</p>
            <p>&copy; <?= date('Y') ?> DERS - Dakoii Echad Recruitment & Selection System. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
