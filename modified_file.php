<?php
/**
 * Exercise Pre-screening Positions Index View
 * Displays positions management for pre-screening selection
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-md-8">
            <h2><i class="fas fa-list-check me-2"></i>Pre-Screening Positions</h2>
            <p class="text-muted">Select which positions must go through pre-screening for this exercise</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= base_url('exercise_settings/' . $exercise['id']) ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Settings
            </a>
        </div>
    </div>

    <!-- Exercise Information Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Exercise Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Exercise Name:</strong> <?= esc($exercise['exercise_name']) ?></p>
                            <p><strong>Advertisement No:</strong> <?= esc($exercise['advertisement_no'] ?? 'N/A') ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Status:</strong> 
                                <span class="badge <?= $exercise['status'] === 'draft' ? 'bg-secondary' : ($exercise['status'] === 'publish' ? 'bg-success' : ($exercise['status'] === 'selection' ? 'bg-primary' : 'bg-danger')) ?>">
                                    <?= ucfirst(esc($exercise['status'])) ?>
                                </span>
                            </p>
                            <p><strong>Organization:</strong> <?= esc($exercise['org_id']) ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <!-- Two Tables Layout -->
    <div class="row">
        <!-- Left Table - Available Positions -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>Available Positions
                        <span class="badge bg-dark ms-2"><?= count($availablePositions) ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($availablePositions)): ?>
                        <!-- Search Bar for Available Positions -->
                        <div class="mb-3">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" id="availablePositionsSearch" class="form-control" placeholder="Search available positions...">
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table id="availablePositionsTable" class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Group</th>
                                        <th>Position</th>
                                        <th>Reference</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($availablePositions as $position): ?>
                                        <tr>
                                            <td>
                                                <small class="text-muted"><?= esc($position['group_name'] ?? 'No Group') ?></small>
                                            </td>
                                            <td>
                                                <strong><?= esc($position['designation']) ?></strong>
                                                <br>
                                                <small class="text-muted"><?= esc($position['classification']) ?></small>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?= esc($position['position_reference']) ?></span>
                                            </td>
                                            <td>
                                                <form method="POST" action="<?= base_url('exercise_prescreening_positions/add') ?>" style="display: inline;">
                                                    <?= csrf_field() ?>
                                                    <input type="hidden" name="exercise_id" value="<?= $exercise['id'] ?>">
                                                    <input type="hidden" name="position_id" value="<?= $position['id'] ?>">
                                                    <button type="submit"
                                                            class="btn btn-sm btn-success"
                                                            title="Add to Pre-screening"
                                                            onclick="return confirm('Are you sure you want to add &quot;<?= esc($position['designation']) ?>&quot; to pre-screening?')">
                                                        <i class="fas fa-plus me-1"></i> Add
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            All positions are already marked for pre-screening, or no positions are available for this exercise.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Right Table - Pre-screening Positions -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-check-circle me-2"></i>Pre-screening Positions
                        <span class="badge bg-light text-dark ms-2"><?= count($preScreeningPositions) ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($preScreeningPositions)): ?>
                        <!-- Search Bar for Pre-screening Positions -->
                        <div class="mb-3">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" id="preScreeningPositionsSearch" class="form-control" placeholder="Search pre-screening positions...">
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table id="preScreeningPositionsTable" class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Group</th>
                                        <th>Position</th>
                                        <th>Reference</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($preScreeningPositions as $position): ?>
                                        <tr>
                                            <td>
                                                <small class="text-muted"><?= esc($position['group_name'] ?? 'No Group') ?></small>
                                            </td>
                                            <td>
                                                <strong><?= esc($position['designation']) ?></strong>
                                                <br>
                                                <small class="text-success">
                                                    <i class="fas fa-clock me-1"></i>
                                                    Added: <?= date('M j, Y', strtotime($position['created_at'])) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?= esc($position['position_reference']) ?></span>
                                            </td>
                                            <td>
                                                <form method="POST" action="<?= base_url('exercise_prescreening_positions/remove') ?>" style="display: inline;">
                                                    <?= csrf_field() ?>
                                                    <input type="hidden" name="exercise_id" value="<?= $exercise['id'] ?>">
                                                    <input type="hidden" name="position_id" value="<?= $position['position_id'] ?>">
                                                    <button type="submit"
                                                            class="btn btn-sm btn-danger"
                                                            title="Remove from Pre-screening"
                                                            onclick="return confirm('Are you sure you want to remove &quot;<?= esc($position['designation']) ?>&quot; from pre-screening?')">
                                                        <i class="fas fa-times me-1"></i> Remove
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            No positions are currently marked for pre-screening. Add positions from the left table.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable for Available Positions
    var availableTable = $('#availablePositionsTable').DataTable({
        "paging": false,
        "lengthChange": false,
        "searching": true,
        "ordering": true,
        "info": false,
        "autoWidth": false,
        "responsive": true,
        "dom": 't', // Only show table (no search box, info, etc.)
        "language": {
            "emptyTable": "No available positions found",
            "zeroRecords": "No matching positions found"
        },
        "columnDefs": [
            { "orderable": false, "targets": [3] } // Disable sorting on Action column
        ]
    });

    // Initialize DataTable for Pre-screening Positions
    var preScreeningTable = $('#preScreeningPositionsTable').DataTable({
        "paging": false,
        "lengthChange": false,
        "searching": true,
        "ordering": true,
        "info": false,
        "autoWidth": false,
        "responsive": true,
        "dom": 't', // Only show table (no search box, info, etc.)
        "language": {
            "emptyTable": "No positions selected for pre-screening",
            "zeroRecords": "No matching positions found"
        },
        "columnDefs": [
            { "orderable": false, "targets": [3] } // Disable sorting on Action column
        ]
    });

    // Custom search for Available Positions
    $('#availablePositionsSearch').on('keyup change', function() {
        availableTable.search(this.value).draw();
    });

    // Custom search for Pre-screening Positions
    $('#preScreeningPositionsSearch').on('keyup change', function() {
        preScreeningTable.search(this.value).draw();
    });

    // Add real-time search highlighting
    function highlightSearchTerms(table, searchInput) {
        var searchTerm = searchInput.val();
        table.rows().every(function() {
            var row = this.node();
            $(row).find('td').each(function() {
                var cell = $(this);
                var originalText = cell.data('original-text') || cell.text();
                if (!cell.data('original-text')) {
                    cell.data('original-text', originalText);
                }
                
                if (searchTerm && searchTerm.length > 0) {
                    var regex = new RegExp('(' + searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + ')', 'gi');
                    var highlightedText = originalText.replace(regex, '<mark>$1</mark>');
                    if (cell.find('form, button, a').length === 0) { // Don't highlight action columns
                        cell.html(highlightedText);
                    }
                } else {
                    if (cell.find('form, button, a').length === 0) { // Don't modify action columns
                        cell.text(originalText);
                    }
                }
            });
        });
    }

    // Apply highlighting on search
    $('#availablePositionsSearch').on('keyup', function() {
        setTimeout(function() {
            highlightSearchTerms(availableTable, $('#availablePositionsSearch'));
        }, 50);
    });

    $('#preScreeningPositionsSearch').on('keyup', function() {
        setTimeout(function() {
            highlightSearchTerms(preScreeningTable, $('#preScreeningPositionsSearch'));
        }, 50);
    });

    // Clear highlighting when search is cleared
    $('#availablePositionsSearch, #preScreeningPositionsSearch').on('input', function() {
        if ($(this).val() === '') {
            var table = $(this).attr('id') === 'availablePositionsSearch' ? availableTable : preScreeningTable;
            table.rows().every(function() {
                var row = this.node();
                $(row).find('td').each(function() {
                    var cell = $(this);
                    var originalText = cell.data('original-text');
                    if (originalText && cell.find('form, button, a').length === 0) {
                        cell.text(originalText);
                    }
                });
            });
        }
    });

    console.log('Pre-screening positions page loaded with DataTables search functionality');
});
</script>

<style>
/* Custom styling for search highlight */
mark {
    background-color: #fff3cd;
    color: #856404;
    padding: 1px 2px;
    border-radius: 2px;
}

/* Search input styling */
.input-group-text {
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

/* Responsive table improvements */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}

/* DataTable responsive improvements */
.dataTables_wrapper .dataTables_empty {
    text-align: center;
    font-style: italic;
    color: #6c757d;
    padding: 2rem;
}

/* Search box focus state */
#availablePositionsSearch:focus,
#preScreeningPositionsSearch:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}
</style>
<?= $this->endSection() ?>
